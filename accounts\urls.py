from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

urlpatterns = [
    # Authentication
    path('login/', views.login_view, name='login'),
    path('discord/', views.discord_auth, name='discord_auth'),
    path('refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # User Profile
    path('profile/', views.UserProfileView.as_view(), name='user_profile'),
    path('stats/', views.user_stats, name='user_stats'),
    
    # Verification
    path('generate-code/', views.generate_verification_code, name='generate_code'),
    path('verify-code/', views.verify_code_in_chat, name='verify_code'),
    
    # Points & Rewards
    path('daily-reward/', views.claim_daily_reward, name='daily_reward'),
    path('transactions/', views.PointsTransactionListView.as_view(), name='transactions'),
    path('leaderboard/', views.leaderboard, name='leaderboard'),
]
