/* CasinoX Dashboard Styles */

:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #1f2937;
    --light-color: #f9fafb;
    --border-color: #e5e7eb;
}

/* Dashboard Layout */
.dashboard-container {
    min-height: 100vh;
    background: var(--light-color);
}

.sidebar {
    width: 250px;
    background: white;
    border-right: 1px solid var(--border-color);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.main-content {
    margin-left: 250px;
    padding: 20px;
}

.sidebar-nav {
    padding: 20px 0;
}

.sidebar-nav .nav-link {
    color: var(--dark-color);
    padding: 12px 20px;
    border: none;
    border-radius: 0;
    transition: all 0.3s ease;
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
    background: var(--primary-color);
    color: white;
}

.sidebar-nav .nav-link i {
    margin-right: 10px;
    width: 20px;
}

/* Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: none;
}

.stat-card {
    text-align: center;
    padding: 30px 20px;
}

.stat-card .stat-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.8;
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-card .stat-label {
    color: #6b7280;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-card.primary .stat-icon { color: var(--primary-color); }
.stat-card.success .stat-icon { color: var(--success-color); }
.stat-card.warning .stat-icon { color: var(--warning-color); }
.stat-card.danger .stat-icon { color: var(--danger-color); }

/* Wheel Configuration */
.wheel-preview {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    margin: 20px auto;
    position: relative;
    border: 3px solid var(--primary-color);
    overflow: hidden;
}

.wheel-segment {
    position: absolute;
    width: 50%;
    height: 50%;
    transform-origin: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.wheel-pointer {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 30px solid var(--danger-color);
    z-index: 10;
}

/* Game Tables */
.game-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.game-table .table {
    margin-bottom: 0;
}

.game-table .table th {
    background: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.game-table .table td {
    padding: 12px 15px;
    border-color: var(--border-color);
}

/* Forms */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
}

.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 8px;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* Charts */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .mobile-menu-btn {
        display: block;
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 1001;
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 10px;
        border-radius: 5px;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Toggle Switches */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}
