{% extends 'viewer/base.html' %}

{% block content %}
<div class="row">
    <!-- Left Column - Games and Activities -->
    <div class="col-lg-8">
        <!-- Wheel of Fortune Section -->
        <div id="wheel-section" class="card mb-4" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-dharmachakra"></i> Wheel of Fortune</h5>
            </div>
            <div class="card-body text-center">
                <div class="wheel-container mb-4">
                    <div class="wheel-pointer"></div>
                    <div class="wheel" id="wheel">
                        <!-- Wheel segments will be populated by JavaScript -->
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mx-auto">
                        <div class="input-group mb-3">
                            <input type="number" class="form-control" id="wheel-bet-amount" 
                                   placeholder="Bet amount" min="10" max="100">
                            <button class="btn btn-primary" id="spin-wheel-btn">
                                <i class="fas fa-play"></i> Spin Wheel
                            </button>
                        </div>
                        <small class="text-muted">Minimum: 10 points | Maximum: 100 points</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mini Games Section -->
        <div id="games-section" class="card mb-4" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-gamepad"></i> Mini Games</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <!-- Slot Machine -->
                    <div class="col-md-4">
                        <div class="card game-card h-100" data-game="slot">
                            <div class="card-body text-center">
                                <i class="fas fa-coins fa-3x text-warning mb-3"></i>
                                <h6>Slot Machine</h6>
                                <p class="small text-muted">Match symbols to win big!</p>
                                <button class="btn btn-primary btn-sm game-btn" data-game="slot">
                                    Play Slots
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Dice Roll -->
                    <div class="col-md-4">
                        <div class="card game-card h-100" data-game="dice">
                            <div class="card-body text-center">
                                <i class="fas fa-dice fa-3x text-success mb-3"></i>
                                <h6>Dice Roll</h6>
                                <p class="small text-muted">Predict the dice outcome!</p>
                                <button class="btn btn-primary btn-sm game-btn" data-game="dice">
                                    Roll Dice
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Double or Nothing -->
                    <div class="col-md-4">
                        <div class="card game-card h-100" data-game="double">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-3x text-danger mb-3"></i>
                                <h6>Double or Nothing</h6>
                                <p class="small text-muted">Risk it all for double!</p>
                                <button class="btn btn-primary btn-sm game-btn" data-game="double">
                                    Play Double
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Points Shop Section -->
        <div id="shop-section" class="card mb-4" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shopping-cart"></i> Points Shop</h5>
            </div>
            <div class="card-body">
                <div id="shop-items" class="row g-3">
                    <!-- Shop items will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history"></i> Recent Activity</h5>
            </div>
            <div class="card-body">
                <div id="recent-activity">
                    <!-- Recent activity will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Right Column - Stats and Leaderboard -->
    <div class="col-lg-4">
        <!-- User Stats -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user"></i> Your Stats</h5>
            </div>
            <div class="card-body">
                <div class="points-display mb-3">
                    <div class="h4 mb-0" id="user-points">0</div>
                    <small>Points</small>
                </div>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h6 mb-0" id="user-games">0</div>
                        <small class="text-muted">Games Played</small>
                    </div>
                    <div class="col-6">
                        <div class="h6 mb-0" id="user-wins">0</div>
                        <small class="text-muted">Wins</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Leaderboard -->
        <div id="leaderboard-section" class="card mb-4" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-trophy"></i> Leaderboard</h5>
            </div>
            <div class="card-body p-0">
                <div id="leaderboard-list">
                    <!-- Leaderboard will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="window.open('https://kick.com/{{ streamer.kick_channel }}', '_blank')">
                        <i class="fas fa-external-link-alt"></i> Watch Stream
                    </button>
                    <button class="btn btn-outline-secondary" id="view-history">
                        <i class="fas fa-history"></i> Game History
                    </button>
                    <button class="btn btn-outline-info" id="view-stats">
                        <i class="fas fa-chart-bar"></i> Detailed Stats
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Game Modals -->
<!-- Slot Machine Modal -->
<div class="modal fade" id="slot-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Slot Machine</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <div id="slot-reels" class="d-flex justify-content-center gap-2 mb-3">
                        <div class="slot-reel border p-3 rounded">🍒</div>
                        <div class="slot-reel border p-3 rounded">🍋</div>
                        <div class="slot-reel border p-3 rounded">🍊</div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-8 mx-auto">
                        <div class="input-group">
                            <input type="number" class="form-control" id="slot-bet-amount" 
                                   placeholder="Bet amount" min="10" max="1000" value="10">
                            <button class="btn btn-primary" id="play-slot-btn">
                                <i class="fas fa-play"></i> Spin
                            </button>
                        </div>
                        <small class="text-muted">Min: 10 points | Max: 1000 points</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dice Roll Modal -->
<div class="modal fade" id="dice-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Dice Roll</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <div id="dice-display" class="d-flex justify-content-center gap-2 mb-3">
                        <div class="dice border p-3 rounded">🎲</div>
                        <div class="dice border p-3 rounded">🎲</div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-10 mx-auto">
                        <div class="mb-3">
                            <label class="form-label">Bet Type:</label>
                            <select class="form-select" id="dice-bet-type">
                                <option value="over">Over</option>
                                <option value="under">Under</option>
                                <option value="exact">Exact</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Target Number:</label>
                            <input type="number" class="form-control" id="dice-bet-value" 
                                   min="2" max="12" value="7">
                        </div>
                        
                        <div class="input-group">
                            <input type="number" class="form-control" id="dice-bet-amount" 
                                   placeholder="Bet amount" min="5" max="500" value="5">
                            <button class="btn btn-primary" id="roll-dice-btn">
                                <i class="fas fa-dice"></i> Roll
                            </button>
                        </div>
                        <small class="text-muted">Min: 5 points | Max: 500 points</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize viewer with streamer username
    document.addEventListener('DOMContentLoaded', function() {
        // Game event listeners
        document.getElementById('spin-wheel-btn')?.addEventListener('click', function() {
            const betAmount = document.getElementById('wheel-bet-amount').value;
            if (betAmount && betAmount >= 10) {
                // This would be handled by the viewer.js
                console.log('Spinning wheel with bet:', betAmount);
            }
        });
        
        document.getElementById('play-slot-btn')?.addEventListener('click', function() {
            const betAmount = document.getElementById('slot-bet-amount').value;
            if (betAmount && betAmount >= 10) {
                // This would be handled by the viewer.js
                console.log('Playing slots with bet:', betAmount);
            }
        });
        
        document.getElementById('roll-dice-btn')?.addEventListener('click', function() {
            const betAmount = document.getElementById('dice-bet-amount').value;
            const betType = document.getElementById('dice-bet-type').value;
            const betValue = document.getElementById('dice-bet-value').value;
            
            if (betAmount && betAmount >= 5) {
                // This would be handled by the viewer.js
                console.log('Rolling dice:', { betAmount, betType, betValue });
            }
        });
    });
</script>
{% endblock %}
