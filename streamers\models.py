from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()


class SubscriptionPlan(models.Model):
    """Subscription plans for streamers"""
    PLAN_TYPES = (
        ('basic', 'Basic'),
        ('pro', 'Pro'),
        ('ultimate', 'Ultimate'),
    )
    
    name = models.CharField(max_length=50, choices=PLAN_TYPES, unique=True)
    display_name = models.CharField(max_length=100)
    price_monthly = models.DecimalField(max_digits=10, decimal_places=2)
    features = models.JSONField(default=dict)  # Store plan features as JSON
    max_concurrent_users = models.IntegerField(default=100)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.display_name


class Streamer(models.Model):
    """Streamer profile and settings"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    kick_channel = models.CharField(max_length=100, unique=True)
    display_name = models.CharField(max_length=100)
    bio = models.TextField(blank=True)
    avatar = models.ImageField(upload_to='streamers/avatars/', null=True, blank=True)
    banner = models.ImageField(upload_to='streamers/banners/', null=True, blank=True)
    
    # Subscription
    subscription_plan = models.ForeignKey(SubscriptionPlan, on_delete=models.SET_NULL, null=True)
    subscription_active = models.BooleanField(default=False)
    subscription_expires_at = models.DateTimeField(null=True, blank=True)
    
    # Settings
    is_active = models.BooleanField(default=True)
    timezone = models.CharField(max_length=50, default='UTC')
    primary_color = models.CharField(max_length=7, default='#6366f1')  # Hex color
    secondary_color = models.CharField(max_length=7, default='#8b5cf6')
    
    # Features enabled
    wheel_enabled = models.BooleanField(default=True)
    games_enabled = models.BooleanField(default=False)
    shop_enabled = models.BooleanField(default=False)
    leaderboard_enabled = models.BooleanField(default=True)
    obs_overlays_enabled = models.BooleanField(default=False)
    
    # Chat bot settings
    bot_enabled = models.BooleanField(default=True)
    auto_reply_enabled = models.BooleanField(default=True)
    command_cooldown = models.IntegerField(default=5)  # seconds
    
    # Analytics
    total_viewers = models.IntegerField(default=0)
    total_games_played = models.IntegerField(default=0)
    total_points_distributed = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.display_name} (@{self.kick_channel})"

    def has_feature(self, feature_name):
        """Check if streamer has access to a specific feature"""
        if not self.subscription_plan or not self.subscription_active:
            # Basic features for free users
            return feature_name in ['wheel', 'leaderboard', 'basic_chat']
        
        plan_features = self.subscription_plan.features
        return plan_features.get(feature_name, False)

    def is_subscription_active(self):
        """Check if subscription is currently active"""
        if not self.subscription_expires_at:
            return False
        return timezone.now() < self.subscription_expires_at


class WheelConfiguration(models.Model):
    """Wheel of Fortune configuration for each streamer"""
    streamer = models.OneToOneField(Streamer, on_delete=models.CASCADE)
    
    # Wheel settings
    trigger_keyword = models.CharField(max_length=50, default='!wheel')
    min_points_to_spin = models.IntegerField(default=10)
    max_points_per_spin = models.IntegerField(default=100)
    spin_cooldown = models.IntegerField(default=60)  # seconds
    auto_remove_idle = models.BooleanField(default=True)
    idle_timeout = models.IntegerField(default=300)  # seconds
    
    # Wheel prizes (JSON format)
    prizes = models.JSONField(default=list)
    
    # Visual settings
    wheel_colors = models.JSONField(default=list)
    show_in_obs = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Wheel config for {self.streamer.display_name}"


class ShopConfiguration(models.Model):
    """Shop configuration for each streamer"""
    streamer = models.OneToOneField(Streamer, on_delete=models.CASCADE)
    
    # Shop settings
    is_enabled = models.BooleanField(default=False)
    welcome_message = models.TextField(default="Welcome to the shop!")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Shop config for {self.streamer.display_name}"


class ShopItem(models.Model):
    """Items available in streamer's shop"""
    shop = models.ForeignKey(ShopConfiguration, on_delete=models.CASCADE, related_name='items')
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    price = models.IntegerField()  # Price in points
    image = models.ImageField(upload_to='shop/items/', null=True, blank=True)
    
    # Availability
    is_available = models.BooleanField(default=True)
    stock_quantity = models.IntegerField(null=True, blank=True)  # None = unlimited
    max_per_user = models.IntegerField(null=True, blank=True)
    
    # Metadata
    category = models.CharField(max_length=50, default='general')
    sort_order = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['sort_order', 'name']

    def __str__(self):
        return f"{self.name} - {self.price} points"


class ShopPurchase(models.Model):
    """Track shop purchases"""
    item = models.ForeignKey(ShopItem, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    quantity = models.IntegerField(default=1)
    total_price = models.IntegerField()
    
    # Status
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Metadata
    purchase_id = models.UUIDField(default=uuid.uuid4, unique=True)
    notes = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} bought {self.item.name}"


class ChatCommand(models.Model):
    """Custom chat commands for each streamer"""
    streamer = models.ForeignKey(Streamer, on_delete=models.CASCADE, related_name='chat_commands')
    
    command = models.CharField(max_length=50)  # e.g., "!points", "!shop"
    response = models.TextField()
    is_enabled = models.BooleanField(default=True)
    cooldown = models.IntegerField(default=5)  # seconds
    
    # Permissions
    mod_only = models.BooleanField(default=False)
    subscriber_only = models.BooleanField(default=False)
    
    # Usage stats
    usage_count = models.IntegerField(default=0)
    last_used = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['streamer', 'command']

    def __str__(self):
        return f"{self.streamer.display_name}: {self.command}"


class OBSOverlay(models.Model):
    """OBS overlay configurations"""
    OVERLAY_TYPES = (
        ('wheel', 'Wheel of Fortune'),
        ('leaderboard', 'Leaderboard'),
        ('recent_winner', 'Recent Winner'),
        ('shop_purchase', 'Shop Purchase'),
        ('game_result', 'Game Result'),
    )
    
    streamer = models.ForeignKey(Streamer, on_delete=models.CASCADE, related_name='obs_overlays')
    overlay_type = models.CharField(max_length=20, choices=OVERLAY_TYPES)
    name = models.CharField(max_length=100)
    
    # Configuration
    settings = models.JSONField(default=dict)
    css_styles = models.TextField(blank=True)
    is_enabled = models.BooleanField(default=True)
    
    # Position and size
    width = models.IntegerField(default=400)
    height = models.IntegerField(default=300)
    
    # Access
    overlay_id = models.UUIDField(default=uuid.uuid4, unique=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.streamer.display_name}: {self.name}"

    def get_overlay_url(self):
        """Get the URL for this overlay"""
        return f"/obs/{self.overlay_id}/"
