from django.contrib import admin
from .models import StreamerAnalytics


@admin.register(StreamerAnalytics)
class StreamerAnalyticsAdmin(admin.ModelAdmin):
    """Admin interface for streamer analytics"""
    list_display = [
        'streamer', 'date', 'unique_visitors', 'total_games',
        'points_distributed', 'chat_messages', 'created_at'
    ]
    list_filter = ['date', 'streamer']
    search_fields = ['streamer__display_name']
    readonly_fields = ['created_at']
    ordering = ['-date']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('streamer')
