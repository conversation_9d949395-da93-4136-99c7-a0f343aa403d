from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid
import random
import json

User = get_user_model()


class GameSession(models.Model):
    """Base model for all game sessions"""
    GAME_TYPES = (
        ('wheel', 'Wheel of Fortune'),
        ('slot', 'Slot Machine'),
        ('dice', 'Dice Roll'),
        ('double', 'Double or Nothing'),
        ('crash', 'Crash Game'),
    )

    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    )

    session_id = models.UUIDField(default=uuid.uuid4, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    streamer = models.ForeignKey('streamers.Streamer', on_delete=models.CASCADE)
    game_type = models.CharField(max_length=20, choices=GAME_TYPES)

    # Betting
    bet_amount = models.IntegerField()
    potential_win = models.IntegerField(null=True, blank=True)
    actual_win = models.IntegerField(default=0)

    # Game state
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    game_data = models.JSONField(default=dict)  # Store game-specific data
    result_data = models.JSONField(default=dict)  # Store game results

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.game_type} - {self.bet_amount} points"

    def start_game(self):
        """Start the game session"""
        self.status = 'active'
        self.started_at = timezone.now()
        self.save()

    def complete_game(self, win_amount=0, result_data=None):
        """Complete the game session"""
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.actual_win = win_amount
        if result_data:
            self.result_data = result_data
        self.save()

        # Update user stats
        self.user.total_games_played += 1
        if win_amount > 0:
            self.user.total_wins += 1
            self.user.add_points(win_amount, f"{self.game_type} win")
        self.user.save()


class WheelSpin(models.Model):
    """Wheel of Fortune spins"""
    session = models.OneToOneField(GameSession, on_delete=models.CASCADE)

    # Wheel configuration at time of spin
    prizes = models.JSONField()
    selected_prize_index = models.IntegerField()
    selected_prize = models.JSONField()

    # Animation data
    spin_duration = models.FloatField(default=3.0)  # seconds
    final_rotation = models.FloatField()  # degrees

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Wheel spin by {self.session.user.username}"

    @classmethod
    def create_spin(cls, user, streamer, bet_amount, ip_address=None):
        """Create a new wheel spin"""
        from streamers.models import WheelConfiguration

        # Get wheel configuration
        try:
            wheel_config = WheelConfiguration.objects.get(streamer=streamer)
        except WheelConfiguration.DoesNotExist:
            raise ValueError("Wheel not configured for this streamer")

        # Validate bet amount
        if bet_amount < wheel_config.min_points_to_spin:
            raise ValueError(f"Minimum bet is {wheel_config.min_points_to_spin} points")
        if bet_amount > wheel_config.max_points_per_spin:
            raise ValueError(f"Maximum bet is {wheel_config.max_points_per_spin} points")

        # Check if user has enough points
        if user.points < bet_amount:
            raise ValueError("Insufficient points")

        # Deduct bet amount
        user.deduct_points(bet_amount, "Wheel spin bet")

        # Select prize based on probability
        prizes = wheel_config.prizes
        total_probability = sum(prize['probability'] for prize in prizes)
        random_value = random.randint(1, total_probability)

        cumulative_probability = 0
        selected_prize_index = 0
        for i, prize in enumerate(prizes):
            cumulative_probability += prize['probability']
            if random_value <= cumulative_probability:
                selected_prize_index = i
                break

        selected_prize = prizes[selected_prize_index]

        # Calculate win amount (prize value * bet multiplier)
        win_amount = int(selected_prize['value'] * (bet_amount / wheel_config.min_points_to_spin))

        # Calculate final rotation for animation
        prize_angle = 360 / len(prizes)
        target_angle = selected_prize_index * prize_angle
        final_rotation = 360 * 5 + target_angle  # 5 full rotations + target

        # Create game session
        session = GameSession.objects.create(
            user=user,
            streamer=streamer,
            game_type='wheel',
            bet_amount=bet_amount,
            potential_win=win_amount,
            ip_address=ip_address,
            game_data={
                'wheel_config_id': wheel_config.id,
                'prizes': prizes,
                'selected_prize_index': selected_prize_index
            }
        )

        # Create wheel spin
        wheel_spin = cls.objects.create(
            session=session,
            prizes=prizes,
            selected_prize_index=selected_prize_index,
            selected_prize=selected_prize,
            final_rotation=final_rotation
        )

        # Start and complete the game
        session.start_game()
        session.complete_game(win_amount, {
            'prize_name': selected_prize['name'],
            'prize_value': selected_prize['value'],
            'final_rotation': final_rotation
        })

        return wheel_spin


class SlotMachine(models.Model):
    """Slot machine games"""
    session = models.OneToOneField(GameSession, on_delete=models.CASCADE)

    # Slot configuration
    reels = models.JSONField()  # Array of reel results
    paylines = models.JSONField()  # Winning paylines
    multiplier = models.FloatField(default=1.0)

    # Symbols
    symbols = models.JSONField(default=list)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Slot game by {self.session.user.username}"

    @classmethod
    def create_game(cls, user, streamer, bet_amount, symbol_choice=None, ip_address=None):
        """Create a new slot machine game"""

        # Define slot symbols and their probabilities
        symbols = [
            {'name': '🍒', 'probability': 30, 'multiplier': 2},
            {'name': '🍋', 'probability': 25, 'multiplier': 3},
            {'name': '🍊', 'probability': 20, 'multiplier': 4},
            {'name': '🍇', 'probability': 15, 'multiplier': 5},
            {'name': '💎', 'probability': 8, 'multiplier': 10},
            {'name': '🎰', 'probability': 2, 'multiplier': 50},
        ]

        # Validate bet amount (minimum 10 points)
        if bet_amount < 10:
            raise ValueError("Minimum bet is 10 points")
        if bet_amount > 1000:
            raise ValueError("Maximum bet is 1000 points")

        # Check if user has enough points
        if user.points < bet_amount:
            raise ValueError("Insufficient points")

        # Deduct bet amount
        user.deduct_points(bet_amount, "Slot machine bet")

        # Generate reel results
        reels = []
        for _ in range(3):  # 3 reels
            reel = []
            for _ in range(3):  # 3 symbols per reel
                total_probability = sum(s['probability'] for s in symbols)
                random_value = random.randint(1, total_probability)

                cumulative_probability = 0
                for symbol in symbols:
                    cumulative_probability += symbol['probability']
                    if random_value <= cumulative_probability:
                        reel.append(symbol['name'])
                        break
            reels.append(reel)

        # Check for winning combinations
        paylines = []
        win_amount = 0

        # Check horizontal lines
        for row in range(3):
            line = [reels[0][row], reels[1][row], reels[2][row]]
            if len(set(line)) == 1:  # All same symbol
                symbol_name = line[0]
                symbol_data = next(s for s in symbols if s['name'] == symbol_name)
                line_win = bet_amount * symbol_data['multiplier']
                win_amount += line_win
                paylines.append({
                    'type': 'horizontal',
                    'row': row,
                    'symbols': line,
                    'win': line_win
                })

        # Check diagonal lines
        diagonal1 = [reels[0][0], reels[1][1], reels[2][2]]
        diagonal2 = [reels[0][2], reels[1][1], reels[2][0]]

        for i, diagonal in enumerate([diagonal1, diagonal2]):
            if len(set(diagonal)) == 1:
                symbol_name = diagonal[0]
                symbol_data = next(s for s in symbols if s['name'] == symbol_name)
                line_win = bet_amount * symbol_data['multiplier']
                win_amount += line_win
                paylines.append({
                    'type': 'diagonal',
                    'diagonal': i + 1,
                    'symbols': diagonal,
                    'win': line_win
                })

        # Create game session
        session = GameSession.objects.create(
            user=user,
            streamer=streamer,
            game_type='slot',
            bet_amount=bet_amount,
            potential_win=win_amount,
            ip_address=ip_address,
            game_data={
                'symbols': symbols,
                'reels': reels,
                'paylines': paylines
            }
        )

        # Create slot machine record
        slot_game = cls.objects.create(
            session=session,
            reels=reels,
            paylines=paylines,
            symbols=symbols
        )

        # Start and complete the game
        session.start_game()
        session.complete_game(win_amount, {
            'reels': reels,
            'paylines': paylines,
            'total_win': win_amount
        })

        return slot_game


class DiceRoll(models.Model):
    """Dice roll games"""
    session = models.OneToOneField(GameSession, on_delete=models.CASCADE)

    # Dice configuration
    num_dice = models.IntegerField(default=2)
    dice_results = models.JSONField()
    total_roll = models.IntegerField()

    # Betting
    bet_type = models.CharField(max_length=20)  # 'over', 'under', 'exact'
    bet_value = models.IntegerField()

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Dice roll by {self.session.user.username}"

    @classmethod
    def create_game(cls, user, streamer, bet_amount, bet_type, bet_value, ip_address=None):
        """Create a new dice roll game"""

        # Validate bet amount
        if bet_amount < 5:
            raise ValueError("Minimum bet is 5 points")
        if bet_amount > 500:
            raise ValueError("Maximum bet is 500 points")

        # Validate bet type and value
        if bet_type not in ['over', 'under', 'exact']:
            raise ValueError("Invalid bet type")

        if bet_type in ['over', 'under'] and not (2 <= bet_value <= 12):
            raise ValueError("Bet value must be between 2 and 12")
        elif bet_type == 'exact' and not (2 <= bet_value <= 12):
            raise ValueError("Exact bet value must be between 2 and 12")

        # Check if user has enough points
        if user.points < bet_amount:
            raise ValueError("Insufficient points")

        # Deduct bet amount
        user.deduct_points(bet_amount, "Dice roll bet")

        # Roll dice
        dice_results = [random.randint(1, 6), random.randint(1, 6)]
        total_roll = sum(dice_results)

        # Calculate win
        win_amount = 0
        if bet_type == 'over' and total_roll > bet_value:
            multiplier = 2.0 - (bet_value - 2) * 0.1  # Higher bet_value = lower multiplier
            win_amount = int(bet_amount * multiplier)
        elif bet_type == 'under' and total_roll < bet_value:
            multiplier = 2.0 - (12 - bet_value) * 0.1  # Lower bet_value = lower multiplier
            win_amount = int(bet_amount * multiplier)
        elif bet_type == 'exact' and total_roll == bet_value:
            # Exact bets have higher multipliers
            exact_multipliers = {
                2: 30, 3: 15, 4: 10, 5: 8, 6: 6,
                7: 5, 8: 6, 9: 8, 10: 10, 11: 15, 12: 30
            }
            win_amount = bet_amount * exact_multipliers.get(bet_value, 5)

        # Create game session
        session = GameSession.objects.create(
            user=user,
            streamer=streamer,
            game_type='dice',
            bet_amount=bet_amount,
            potential_win=win_amount,
            ip_address=ip_address,
            game_data={
                'bet_type': bet_type,
                'bet_value': bet_value,
                'dice_results': dice_results,
                'total_roll': total_roll
            }
        )

        # Create dice roll record
        dice_game = cls.objects.create(
            session=session,
            num_dice=2,
            dice_results=dice_results,
            total_roll=total_roll,
            bet_type=bet_type,
            bet_value=bet_value
        )

        # Start and complete the game
        session.start_game()
        session.complete_game(win_amount, {
            'dice_results': dice_results,
            'total_roll': total_roll,
            'bet_type': bet_type,
            'bet_value': bet_value,
            'won': win_amount > 0
        })

        return dice_game


class GameStatistics(models.Model):
    """Statistics for games per streamer"""
    streamer = models.OneToOneField('streamers.Streamer', on_delete=models.CASCADE)

    # Overall stats
    total_games = models.IntegerField(default=0)
    total_bets = models.BigIntegerField(default=0)
    total_wins = models.BigIntegerField(default=0)
    total_players = models.IntegerField(default=0)

    # Game-specific stats
    wheel_games = models.IntegerField(default=0)
    slot_games = models.IntegerField(default=0)
    dice_games = models.IntegerField(default=0)

    # Daily stats
    today_games = models.IntegerField(default=0)
    today_bets = models.BigIntegerField(default=0)
    today_wins = models.BigIntegerField(default=0)
    last_reset = models.DateField(auto_now_add=True)

    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Stats for {self.streamer.display_name}"

    def reset_daily_stats(self):
        """Reset daily statistics"""
        self.today_games = 0
        self.today_bets = 0
        self.today_wins = 0
        self.last_reset = timezone.now().date()
        self.save()
