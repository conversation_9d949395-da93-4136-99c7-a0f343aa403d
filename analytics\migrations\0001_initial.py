# Generated by Django 4.2.7 on 2025-07-18 20:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("streamers", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="StreamerAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField()),
                ("unique_visitors", models.IntegerField(default=0)),
                ("new_users", models.IntegerField(default=0)),
                ("returning_users", models.IntegerField(default=0)),
                ("verified_users", models.IntegerField(default=0)),
                ("total_games", models.IntegerField(default=0)),
                ("wheel_spins", models.IntegerField(default=0)),
                ("slot_games", models.IntegerField(default=0)),
                ("dice_games", models.IntegerField(default=0)),
                ("points_distributed", models.BigIntegerField(default=0)),
                ("points_spent", models.BigIntegerField(default=0)),
                ("shop_purchases", models.IntegerField(default=0)),
                ("chat_messages", models.IntegerField(default=0)),
                ("bot_responses", models.IntegerField(default=0)),
                ("commands_used", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "streamer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="streamers.streamer",
                    ),
                ),
            ],
            options={
                "unique_together": {("streamer", "date")},
            },
        ),
    ]
