from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class StreamerAnalytics(models.Model):
    """Daily analytics for streamers"""
    streamer = models.ForeignKey('streamers.Streamer', on_delete=models.CASCADE)
    date = models.DateField()
    
    # User metrics
    unique_visitors = models.IntegerField(default=0)
    new_users = models.IntegerField(default=0)
    returning_users = models.IntegerField(default=0)
    verified_users = models.IntegerField(default=0)
    
    # Game metrics
    total_games = models.IntegerField(default=0)
    wheel_spins = models.IntegerField(default=0)
    slot_games = models.IntegerField(default=0)
    dice_games = models.IntegerField(default=0)
    
    # Points metrics
    points_distributed = models.BigIntegerField(default=0)
    points_spent = models.BigIntegerField(default=0)
    shop_purchases = models.IntegerField(default=0)
    
    # Chat metrics
    chat_messages = models.IntegerField(default=0)
    bot_responses = models.IntegerField(default=0)
    commands_used = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['streamer', 'date']

    def __str__(self):
        return f"Analytics for {self.streamer.display_name} - {self.date}"
