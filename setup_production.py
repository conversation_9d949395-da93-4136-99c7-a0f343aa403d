#!/usr/bin/env python3
"""
Production setup script for CasinoX
"""

import os
import sys
import subprocess
from decouple import config

def setup_postgresql():
    """Setup PostgreSQL for production"""
    print("🐘 Setting up PostgreSQL for Production")
    print("=" * 50)
    
    # Update .env for PostgreSQL
    env_content = """# Production Configuration
DEBUG=False
USE_SQLITE=False
USE_REDIS=True

# Database Configuration (PostgreSQL)
DB_NAME=casinox_prod
DB_USER=casinox_user
DB_PASSWORD=your_secure_password_here
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Discord OAuth Configuration
DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret
DISCORD_REDIRECT_URI=https://yourdomain.com/auth/discord/callback/

# Payment Configuration
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_key
STRIPE_SECRET_KEY=sk_live_your_stripe_key
NOWPAYMENTS_API_KEY=your_nowpayments_key

# Security
SECRET_KEY=your_production_secret_key_here
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=CasinoX <<EMAIL>>
"""
    
    with open('.env.production', 'w') as f:
        f.write(env_content)
    
    print("✅ Created .env.production template")
    print("⚠️  Update .env.production with your actual values")


def create_docker_production():
    """Create production Docker setup"""
    print("\n🐳 Creating Production Docker Setup")
    print("=" * 50)
    
    # Production docker-compose
    docker_compose_prod = """version: '3.8'

services:
  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      POSTGRES_DB: casinox_prod
      POSTGRES_USER: casinox_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    restart: unless-stopped
    networks:
      - casinox_network

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    networks:
      - casinox_network

  web:
    build: .
    command: gunicorn --bind 0.0.0.0:8000 --workers 4 casinox.wsgi:application
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    env_file:
      - .env.production
    restart: unless-stopped
    networks:
      - casinox_network

  celery:
    build: .
    command: celery -A casinox worker -l info
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    env_file:
      - .env.production
    restart: unless-stopped
    networks:
      - casinox_network

  celery-beat:
    build: .
    command: celery -A casinox beat -l info
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    env_file:
      - .env.production
    restart: unless-stopped
    networks:
      - casinox_network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
    restart: unless-stopped
    networks:
      - casinox_network

volumes:
  postgres_data:
  static_volume:
  media_volume:

networks:
  casinox_network:
    driver: bridge
"""
    
    with open('docker-compose.prod.yml', 'w') as f:
        f.write(docker_compose_prod)
    
    print("✅ Created docker-compose.prod.yml")


def create_nginx_config():
    """Create Nginx configuration"""
    print("\n🌐 Creating Nginx Configuration")
    print("=" * 50)
    
    nginx_config = """events {
    worker_connections 1024;
}

http {
    upstream casinox_web {
        server web:8000;
    }

    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;
        
        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

        # Static files
        location /static/ {
            alias /app/staticfiles/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        location /media/ {
            alias /app/media/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # WebSocket support
        location /ws/ {
            proxy_pass http://casinox_web;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Main application
        location / {
            proxy_pass http://casinox_web;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
"""
    
    with open('nginx.conf', 'w') as f:
        f.write(nginx_config)
    
    print("✅ Created nginx.conf")


def create_deployment_scripts():
    """Create deployment scripts"""
    print("\n🚀 Creating Deployment Scripts")
    print("=" * 50)
    
    # Deploy script
    deploy_script = """#!/bin/bash
# Production deployment script for CasinoX

echo "🚀 Deploying CasinoX to Production"
echo "=================================="

# Pull latest code
git pull origin main

# Build and start containers
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Run migrations
docker-compose -f docker-compose.prod.yml exec web python manage.py migrate

# Collect static files
docker-compose -f docker-compose.prod.yml exec web python manage.py collectstatic --noinput

# Create superuser if needed
docker-compose -f docker-compose.prod.yml exec web python manage.py create_superuser --username admin --email <EMAIL> --password admin123

# Setup subscription plans
docker-compose -f docker-compose.prod.yml exec web python manage.py setup_subscription_plans

echo "✅ Deployment completed!"
echo "Visit: https://yourdomain.com"
"""
    
    with open('deploy.sh', 'w') as f:
        f.write(deploy_script)
    
    os.chmod('deploy.sh', 0o755)
    print("✅ Created deploy.sh")
    
    # Backup script
    backup_script = """#!/bin/bash
# Backup script for CasinoX

BACKUP_DIR="/backups/casinox"
DATE=$(date +%Y%m%d_%H%M%S)

echo "📦 Creating backup: $DATE"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup database
docker-compose -f docker-compose.prod.yml exec db pg_dump -U casinox_user casinox_prod > $BACKUP_DIR/db_$DATE.sql

# Backup media files
tar -czf $BACKUP_DIR/media_$DATE.tar.gz media/

# Backup environment
cp .env.production $BACKUP_DIR/env_$DATE.backup

echo "✅ Backup completed: $BACKUP_DIR"
"""
    
    with open('backup.sh', 'w') as f:
        f.write(backup_script)
    
    os.chmod('backup.sh', 0o755)
    print("✅ Created backup.sh")


def create_monitoring_setup():
    """Create monitoring and logging setup"""
    print("\n📊 Creating Monitoring Setup")
    print("=" * 50)
    
    # Add monitoring to docker-compose
    monitoring_services = """
  # Add these services to docker-compose.prod.yml
  
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - casinox_network

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - casinox_network

volumes:
  grafana_data:
"""
    
    with open('monitoring_services.yml', 'w') as f:
        f.write(monitoring_services)
    
    print("✅ Created monitoring_services.yml")
    print("   Add these services to docker-compose.prod.yml")


def print_production_checklist():
    """Print production deployment checklist"""
    print("\n✅ Production Deployment Checklist")
    print("=" * 50)
    
    checklist = [
        "🔐 Update .env.production with real values",
        "🐘 Set up PostgreSQL database",
        "🔴 Set up Redis server",
        "🌐 Configure domain and DNS",
        "🔒 Get SSL certificates (Let's Encrypt)",
        "💳 Configure payment providers (Stripe, NOWPayments)",
        "📧 Set up email service (SMTP)",
        "🔐 Configure Discord OAuth with production domain",
        "🚀 Deploy with: ./deploy.sh",
        "📊 Set up monitoring (Prometheus + Grafana)",
        "📦 Set up automated backups",
        "🔍 Configure error tracking (Sentry)",
        "📈 Set up analytics (Google Analytics)",
        "🛡️ Configure firewall and security",
        "📱 Test all features in production"
    ]
    
    for i, item in enumerate(checklist, 1):
        print(f"{i:2d}. {item}")
    
    print(f"\n🎯 Quick Start Commands:")
    print(f"   Production: docker-compose -f docker-compose.prod.yml up -d")
    print(f"   Deploy: ./deploy.sh")
    print(f"   Backup: ./backup.sh")
    print(f"   Logs: docker-compose -f docker-compose.prod.yml logs -f")


def main():
    """Main setup function"""
    print("🏭 CasinoX Production Setup")
    print("=" * 50)
    
    setup_postgresql()
    create_docker_production()
    create_nginx_config()
    create_deployment_scripts()
    create_monitoring_setup()
    print_production_checklist()
    
    print("\n" + "=" * 50)
    print("🎉 Production setup files created!")
    print("Next: Update .env.production and run ./deploy.sh")


if __name__ == '__main__':
    main()
