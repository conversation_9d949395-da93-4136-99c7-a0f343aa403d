// CasinoX Viewer Interface JavaScript

class CasinoXViewer {
    constructor(streamerUsername) {
        this.streamerUsername = streamerUsername;
        this.websocket = null;
        this.user = null;
        this.verificationCode = null;
        this.init();
    }

    init() {
        this.loadUserData();
        this.setupWebSocket();
        this.setupEventListeners();
        this.loadStreamerData();
    }

    loadUserData() {
        const token = localStorage.getItem('access_token');
        if (token) {
            this.fetchUserProfile();
        }
    }

    async fetchUserProfile() {
        try {
            const response = await fetch('/api/auth/profile/', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });
            
            if (response.ok) {
                this.user = await response.json();
                this.updateUserInterface();
            }
        } catch (error) {
            console.error('Error fetching user profile:', error);
        }
    }

    setupWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/chat/${this.streamerUsername}/`;
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            console.log('Viewer WebSocket connected');
        };
        
        this.websocket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
        };
        
        this.websocket.onclose = () => {
            console.log('Viewer WebSocket disconnected');
            setTimeout(() => this.setupWebSocket(), 5000);
        };
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'wheel_spin':
                this.showWheelResult(data.data);
                break;
            case 'game_result':
                this.showGameResult(data.data);
                break;
            case 'verification_update':
                this.handleVerificationUpdate(data.data);
                break;
            case 'leaderboard_update':
                this.updateLeaderboard(data.data);
                break;
        }
    }

    setupEventListeners() {
        // Discord login
        const discordLoginBtn = document.getElementById('discord-login');
        if (discordLoginBtn) {
            discordLoginBtn.addEventListener('click', () => {
                this.initiateDiscordLogin();
            });
        }

        // Generate verification code
        const generateCodeBtn = document.getElementById('generate-code');
        if (generateCodeBtn) {
            generateCodeBtn.addEventListener('click', () => {
                this.generateVerificationCode();
            });
        }

        // Claim daily reward
        const dailyRewardBtn = document.getElementById('claim-daily-reward');
        if (dailyRewardBtn) {
            dailyRewardBtn.addEventListener('click', () => {
                this.claimDailyReward();
            });
        }

        // Game buttons
        document.querySelectorAll('.game-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const gameType = e.target.dataset.game;
                this.showGameModal(gameType);
            });
        });

        // Shop purchase buttons
        document.querySelectorAll('.purchase-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const itemId = e.target.dataset.itemId;
                this.purchaseItem(itemId);
            });
        });
    }

    async loadStreamerData() {
        try {
            const response = await fetch(`/api/streamers/public/${this.streamerUsername}/`);
            if (response.ok) {
                const streamerData = await response.json();
                this.updateStreamerInterface(streamerData);
            }
        } catch (error) {
            console.error('Error loading streamer data:', error);
        }
    }

    updateUserInterface() {
        if (!this.user) return;

        // Update user info display
        const userInfo = document.getElementById('user-info');
        if (userInfo) {
            userInfo.innerHTML = `
                <div class="d-flex align-items-center">
                    <img src="${this.user.discord_avatar || '/static/img/default-avatar.png'}" 
                         alt="Avatar" class="rounded-circle me-2" width="40" height="40">
                    <div>
                        <div class="fw-bold">${this.user.discord_username || this.user.username}</div>
                        <div class="text-muted small">${this.user.points} points</div>
                    </div>
                </div>
            `;
        }

        // Update verification status
        const verificationStatus = document.getElementById('verification-status');
        if (verificationStatus) {
            if (this.user.is_verified) {
                verificationStatus.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> Verified! You can now play games.
                    </div>
                `;
            } else {
                verificationStatus.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> Please verify your account to play games.
                    </div>
                `;
            }
        }

        // Show/hide daily reward button
        const dailyRewardBtn = document.getElementById('claim-daily-reward');
        if (dailyRewardBtn) {
            dailyRewardBtn.style.display = this.user.can_claim_daily_reward ? 'block' : 'none';
        }
    }

    updateStreamerInterface(streamerData) {
        // Update page title and branding
        document.title = `${streamerData.display_name} - CasinoX`;
        
        // Update color scheme
        document.documentElement.style.setProperty('--primary-color', streamerData.primary_color);
        document.documentElement.style.setProperty('--secondary-color', streamerData.secondary_color);

        // Show/hide features based on streamer settings
        const features = {
            'wheel-section': streamerData.wheel_enabled,
            'games-section': streamerData.games_enabled,
            'shop-section': streamerData.shop_enabled,
            'leaderboard-section': streamerData.leaderboard_enabled
        };

        Object.entries(features).forEach(([sectionId, enabled]) => {
            const section = document.getElementById(sectionId);
            if (section) {
                section.style.display = enabled ? 'block' : 'none';
            }
        });
    }

    initiateDiscordLogin() {
        const clientId = 'YOUR_DISCORD_CLIENT_ID'; // Replace with actual client ID
        const redirectUri = encodeURIComponent(window.location.origin + '/auth/discord/callback/');
        const scope = 'identify';
        
        const discordAuthUrl = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}`;
        
        window.location.href = discordAuthUrl;
    }

    async generateVerificationCode() {
        if (!this.user) {
            this.showNotification('Please login with Discord first', 'warning');
            return;
        }

        try {
            const response = await fetch('/api/auth/generate-code/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                },
                body: JSON.stringify({
                    streamer_username: this.streamerUsername
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.verificationCode = data.code;
                this.showVerificationCode(data);
            } else {
                const error = await response.json();
                this.showNotification(error.error || 'Failed to generate code', 'error');
            }
        } catch (error) {
            console.error('Error generating verification code:', error);
            this.showNotification('Failed to generate verification code', 'error');
        }
    }

    showVerificationCode(data) {
        const modal = document.getElementById('verification-modal');
        const codeDisplay = document.getElementById('verification-code-display');
        const instructions = document.getElementById('verification-instructions');

        if (codeDisplay) {
            codeDisplay.textContent = data.code;
        }

        if (instructions) {
            instructions.innerHTML = `
                <p>Copy this code and paste it in <strong>${this.streamerUsername}'s</strong> Kick chat:</p>
                <div class="alert alert-info">
                    <code>!verify ${data.code}</code>
                </div>
                <p class="text-muted small">Code expires in 5 minutes</p>
            `;
        }

        if (modal) {
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    }

    handleVerificationUpdate(data) {
        if (data.user_id === this.user?.id && data.verified) {
            this.user.is_verified = true;
            this.user.points = data.points;
            this.updateUserInterface();
            this.showNotification('Successfully verified! Welcome to the casino!', 'success');
            
            // Close verification modal if open
            const modal = bootstrap.Modal.getInstance(document.getElementById('verification-modal'));
            if (modal) {
                modal.hide();
            }
        }
    }

    async claimDailyReward() {
        try {
            const response = await fetch('/api/auth/daily-reward/', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.user.points = data.total_points;
                this.updateUserInterface();
                this.showNotification(`Daily reward claimed! +${data.points_earned} points`, 'success');
            } else {
                const error = await response.json();
                this.showNotification(error.error || 'Failed to claim reward', 'error');
            }
        } catch (error) {
            console.error('Error claiming daily reward:', error);
            this.showNotification('Failed to claim daily reward', 'error');
        }
    }

    showGameModal(gameType) {
        if (!this.user?.is_verified) {
            this.showNotification('Please verify your account first', 'warning');
            return;
        }

        const modal = document.getElementById(`${gameType}-modal`);
        if (modal) {
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    }

    async playGame(gameType, gameData) {
        try {
            const response = await fetch(`/api/games/${gameType}/play/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                },
                body: JSON.stringify({
                    ...gameData,
                    streamer_username: this.streamerUsername
                })
            });

            if (response.ok) {
                const result = await response.json();
                this.showGameResult(result);
                this.updateUserPoints();
            } else {
                const error = await response.json();
                this.showNotification(error.error || 'Game failed', 'error');
            }
        } catch (error) {
            console.error('Error playing game:', error);
            this.showNotification('Game failed', 'error');
        }
    }

    showWheelResult(data) {
        // Animate wheel spin and show result
        this.showNotification(`${data.user} won ${data.win_amount} points on the wheel!`, 'success');
    }

    showGameResult(data) {
        // Show game result notification
        if (data.session.user === this.user?.username) {
            const winAmount = data.session.actual_win;
            if (winAmount > 0) {
                this.showNotification(`You won ${winAmount} points!`, 'success');
            } else {
                this.showNotification('Better luck next time!', 'info');
            }
        }
    }

    async updateUserPoints() {
        // Refresh user data to get updated points
        await this.fetchUserProfile();
    }

    async purchaseItem(itemId) {
        if (!this.user?.is_verified) {
            this.showNotification('Please verify your account first', 'warning');
            return;
        }

        try {
            const response = await fetch(`/s/${this.streamerUsername}/shop/purchase/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                },
                body: JSON.stringify({
                    item_id: itemId,
                    quantity: 1
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.user.points = data.remaining_points;
                this.updateUserInterface();
                this.showNotification(data.message, 'success');
            } else {
                const error = await response.json();
                this.showNotification(error.error || 'Purchase failed', 'error');
            }
        } catch (error) {
            console.error('Error purchasing item:', error);
            this.showNotification('Purchase failed', 'error');
        }
    }

    updateLeaderboard(data) {
        const leaderboard = document.getElementById('leaderboard-list');
        if (!leaderboard) return;

        leaderboard.innerHTML = data.map((user, index) => `
            <div class="leaderboard-item d-flex justify-content-between align-items-center p-3 border-bottom">
                <div class="d-flex align-items-center">
                    <span class="rank me-3">#${index + 1}</span>
                    <img src="${user.discord_avatar || '/static/img/default-avatar.png'}" 
                         alt="Avatar" class="rounded-circle me-2" width="30" height="30">
                    <span class="username">${user.username}</span>
                </div>
                <div class="points fw-bold">${user.points} pts</div>
            </div>
        `).join('');
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} notification position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize viewer interface when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const streamerUsername = window.location.pathname.split('/')[2];
    if (streamerUsername) {
        new CasinoXViewer(streamerUsername);
    }
});
