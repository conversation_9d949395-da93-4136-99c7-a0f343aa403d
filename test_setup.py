#!/usr/bin/env python3
"""
Test script to verify CasinoX setup is working correctly
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'casinox.settings')
django.setup()

def test_models():
    """Test that all models can be imported and basic operations work"""
    print("Testing models...")
    
    try:
        # Test User model
        from accounts.models import User, PointsTransaction
        print("✓ Accounts models imported successfully")
        
        # Test Streamer models
        from streamers.models import (
            Streamer, SubscriptionPlan, WheelConfiguration,
            VerificationCode, UserSession
        )
        print("✓ Streamers models imported successfully")
        
        # Test Games models
        from games.models import GameSession, WheelSpin, SlotMachine, DiceRoll
        print("✓ Games models imported successfully")
        
        # Test Chat bot models
        from chat_bot.models import KickChatConnection, ChatMessage
        print("✓ Chat bot models imported successfully")
        
        # Test Payments models
        from payments.models import Payment, CryptoPayment, StripePayment
        print("✓ Payments models imported successfully")
        
        # Test Analytics models
        from analytics.models import StreamerAnalytics
        print("✓ Analytics models imported successfully")
        
        return True
    except Exception as e:
        print(f"✗ Model import failed: {e}")
        return False


def test_database():
    """Test database operations"""
    print("\nTesting database operations...")
    
    try:
        from accounts.models import User
        from streamers.models import SubscriptionPlan
        
        # Test user creation
        user_count = User.objects.count()
        print(f"✓ Users in database: {user_count}")
        
        # Test subscription plans
        plan_count = SubscriptionPlan.objects.count()
        print(f"✓ Subscription plans: {plan_count}")
        
        if plan_count == 0:
            print("⚠️  No subscription plans found. Run: python manage.py setup_subscription_plans")
        
        return True
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False


def test_admin():
    """Test admin user exists"""
    print("\nTesting admin user...")
    
    try:
        from accounts.models import User
        
        admin_user = User.objects.filter(username='admin', is_superuser=True).first()
        if admin_user:
            print("✓ Admin user exists")
            print(f"  Username: {admin_user.username}")
            print(f"  Email: {admin_user.email}")
            print(f"  User type: {admin_user.user_type}")
        else:
            print("⚠️  Admin user not found. Run: python manage.py create_superuser")
        
        return True
    except Exception as e:
        print(f"✗ Admin test failed: {e}")
        return False


def test_settings():
    """Test Django settings"""
    print("\nTesting Django settings...")
    
    try:
        from django.conf import settings
        
        print(f"✓ DEBUG: {settings.DEBUG}")
        print(f"✓ Database: {settings.DATABASES['default']['ENGINE']}")
        print(f"✓ Installed apps: {len(settings.INSTALLED_APPS)} apps")
        
        # Check required apps
        required_apps = [
            'accounts', 'streamers', 'games', 'chat_bot', 
            'payments', 'analytics', 'rest_framework', 'channels'
        ]
        
        for app in required_apps:
            if app in settings.INSTALLED_APPS:
                print(f"✓ {app} app installed")
            else:
                print(f"✗ {app} app missing")
        
        return True
    except Exception as e:
        print(f"✗ Settings test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🎰 CasinoX Setup Verification")
    print("=" * 50)
    
    tests = [
        test_models,
        test_database,
        test_admin,
        test_settings
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! CasinoX is ready to use.")
        print("\nNext steps:")
        print("1. Start the server: python manage.py runserver")
        print("2. Visit: http://localhost:8000/")
        print("3. Admin panel: http://localhost:8000/admin/")
        print("4. Login with: admin / admin123")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
