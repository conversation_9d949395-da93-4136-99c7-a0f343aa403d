from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

from .models import Game<PERSON>ession, WheelSpin, SlotMachine, DiceRoll
from .serializers import (
    GameSessionSerializer, WheelSpinSerializer, CreateWheelSpinSerializer,
    SlotMachineSerializer, CreateSlotGameSerializer,
    DiceRollSerializer, CreateDiceGameSerializer, GameHistorySerializer
)
from streamers.models import Streamer


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def spin_wheel(request):
    """Create a new wheel spin"""
    serializer = CreateWheelSpinSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    bet_amount = serializer.validated_data['bet_amount']
    streamer_username = serializer.validated_data['streamer_username']
    
    try:
        streamer = Streamer.objects.get(user__username=streamer_username)
    except Streamer.DoesNotExist:
        return Response(
            {'error': 'Streamer not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    
    try:
        # Create wheel spin
        ip_address = request.META.get('REMOTE_ADDR')
        wheel_spin = WheelSpin.create_spin(
            user=request.user,
            streamer=streamer,
            bet_amount=bet_amount,
            ip_address=ip_address
        )
        
        # Send real-time update
        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            f'chat_{streamer_username}',
            {
                'type': 'wheel_spin',
                'data': {
                    'user': request.user.username,
                    'bet_amount': bet_amount,
                    'prize': wheel_spin.selected_prize,
                    'win_amount': wheel_spin.session.actual_win,
                    'final_rotation': wheel_spin.final_rotation
                }
            }
        )
        
        # Send to OBS overlays
        async_to_sync(channel_layer.group_send)(
            f'obs_{streamer.obs_overlays.filter(overlay_type="wheel").first().overlay_id}',
            {
                'type': 'wheel_spin',
                'data': {
                    'user': request.user.username,
                    'final_rotation': wheel_spin.final_rotation,
                    'prize': wheel_spin.selected_prize,
                    'win_amount': wheel_spin.session.actual_win
                }
            }
        )
        
        serializer = WheelSpinSerializer(wheel_spin)
        return Response(serializer.data)
        
    except ValueError as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        return Response(
            {'error': 'Failed to create wheel spin'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def play_slots(request):
    """Play slot machine game"""
    serializer = CreateSlotGameSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    bet_amount = serializer.validated_data['bet_amount']
    streamer_username = serializer.validated_data['streamer_username']
    
    try:
        streamer = Streamer.objects.get(user__username=streamer_username)
    except Streamer.DoesNotExist:
        return Response(
            {'error': 'Streamer not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    
    try:
        # Create slot game
        ip_address = request.META.get('REMOTE_ADDR')
        slot_game = SlotMachine.create_game(
            user=request.user,
            streamer=streamer,
            bet_amount=bet_amount,
            ip_address=ip_address
        )
        
        # Send real-time update
        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            f'chat_{streamer_username}',
            {
                'type': 'game_result',
                'data': {
                    'game_type': 'slot',
                    'user': request.user.username,
                    'bet_amount': bet_amount,
                    'win_amount': slot_game.session.actual_win,
                    'reels': slot_game.reels,
                    'paylines': slot_game.paylines
                }
            }
        )
        
        serializer = SlotMachineSerializer(slot_game)
        return Response(serializer.data)
        
    except ValueError as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def roll_dice(request):
    """Play dice roll game"""
    serializer = CreateDiceGameSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    bet_amount = serializer.validated_data['bet_amount']
    bet_type = serializer.validated_data['bet_type']
    bet_value = serializer.validated_data['bet_value']
    streamer_username = serializer.validated_data['streamer_username']
    
    try:
        streamer = Streamer.objects.get(user__username=streamer_username)
    except Streamer.DoesNotExist:
        return Response(
            {'error': 'Streamer not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    
    try:
        # Create dice game
        ip_address = request.META.get('REMOTE_ADDR')
        dice_game = DiceRoll.create_game(
            user=request.user,
            streamer=streamer,
            bet_amount=bet_amount,
            bet_type=bet_type,
            bet_value=bet_value,
            ip_address=ip_address
        )
        
        # Send real-time update
        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            f'chat_{streamer_username}',
            {
                'type': 'game_result',
                'data': {
                    'game_type': 'dice',
                    'user': request.user.username,
                    'bet_amount': bet_amount,
                    'win_amount': dice_game.session.actual_win,
                    'dice_results': dice_game.dice_results,
                    'total_roll': dice_game.total_roll,
                    'bet_type': bet_type,
                    'bet_value': bet_value
                }
            }
        )
        
        serializer = DiceRollSerializer(dice_game)
        return Response(serializer.data)
        
    except ValueError as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )


class GameHistoryView(generics.ListAPIView):
    """Get user's game history"""
    serializer_class = GameHistorySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return GameSession.objects.filter(
            user=self.request.user,
            status='completed'
        ).order_by('-completed_at')


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def game_stats(request):
    """Get user's game statistics"""
    user = request.user
    
    # Get game statistics
    total_games = GameSession.objects.filter(user=user, status='completed').count()
    total_wins = GameSession.objects.filter(user=user, status='completed', actual_win__gt=0).count()
    total_bet = GameSession.objects.filter(user=user, status='completed').aggregate(
        total=models.Sum('bet_amount')
    )['total'] or 0
    total_won = GameSession.objects.filter(user=user, status='completed').aggregate(
        total=models.Sum('actual_win')
    )['total'] or 0
    
    # Calculate win rate
    win_rate = (total_wins / total_games * 100) if total_games > 0 else 0
    
    # Game type breakdown
    wheel_games = GameSession.objects.filter(user=user, game_type='wheel', status='completed').count()
    slot_games = GameSession.objects.filter(user=user, game_type='slot', status='completed').count()
    dice_games = GameSession.objects.filter(user=user, game_type='dice', status='completed').count()
    
    return Response({
        'total_games': total_games,
        'total_wins': total_wins,
        'win_rate': round(win_rate, 2),
        'total_bet': total_bet,
        'total_won': total_won,
        'net_result': total_won - total_bet,
        'game_breakdown': {
            'wheel': wheel_games,
            'slot': slot_games,
            'dice': dice_games
        }
    })
