#!/usr/bin/env python3
"""
Simple outreach tracking for CasinoX
"""

import json
from datetime import datetime

def add_new_contact():
    """Add a new contact to tracking"""
    print("📧 Add New Contact")
    print("=" * 30)
    
    # Get contact info
    name = input("Streamer name: ")
    platform = input("Platform (kick/discord/twitter): ")
    channel = input("Channel/username: ")
    viewers = input("Typical viewer count: ")
    message_type = input("Message type (dm/chat/email): ")
    
    contact = {
        "name": name,
        "platform": platform,
        "channel": channel,
        "viewers": viewers,
        "message_type": message_type,
        "date_contacted": datetime.now().isoformat(),
        "response_received": False,
        "response_date": None,
        "demo_interest": False,
        "trial_started": False,
        "converted": False,
        "notes": ""
    }
    
    # Load existing contacts
    try:
        with open('contacts.json', 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        data = {"contacts": []}
    
    # Add new contact
    data["contacts"].append(contact)
    
    # Save
    with open('contacts.json', 'w') as f:
        json.dump(data, f, indent=2)
    
    print(f"✅ Added {name} to tracking!")
    return contact

def view_contacts():
    """View all contacts"""
    try:
        with open('contacts.json', 'r') as f:
            data = json.load(f)
        
        print("📊 Contact Summary")
        print("=" * 30)
        
        if not data["contacts"]:
            print("No contacts yet. Add your first one!")
            return
        
        for i, contact in enumerate(data["contacts"], 1):
            status = "✅ Responded" if contact["response_received"] else "⏳ Waiting"
            print(f"{i}. {contact['name']} ({contact['platform']}) - {status}")
            print(f"   Contacted: {contact['date_contacted'][:10]}")
            if contact["notes"]:
                print(f"   Notes: {contact['notes']}")
            print()
        
        # Stats
        total = len(data["contacts"])
        responded = sum(1 for c in data["contacts"] if c["response_received"])
        trials = sum(1 for c in data["contacts"] if c["trial_started"])
        converted = sum(1 for c in data["contacts"] if c["converted"])
        
        print(f"📈 Stats:")
        print(f"   Total contacted: {total}")
        print(f"   Responses: {responded} ({responded/total*100:.1f}%)")
        print(f"   Trials: {trials}")
        print(f"   Customers: {converted}")
        
    except FileNotFoundError:
        print("No contacts file found. Add your first contact!")

def update_contact():
    """Update contact status"""
    view_contacts()
    
    try:
        with open('contacts.json', 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("No contacts to update!")
        return
    
    if not data["contacts"]:
        print("No contacts to update!")
        return
    
    try:
        index = int(input("Enter contact number to update: ")) - 1
        contact = data["contacts"][index]
        
        print(f"Updating: {contact['name']}")
        
        # Update fields
        if input("Did they respond? (y/n): ").lower() == 'y':
            contact["response_received"] = True
            contact["response_date"] = datetime.now().isoformat()
        
        if input("Interested in demo? (y/n): ").lower() == 'y':
            contact["demo_interest"] = True
        
        if input("Started trial? (y/n): ").lower() == 'y':
            contact["trial_started"] = True
        
        if input("Converted to paid? (y/n): ").lower() == 'y':
            contact["converted"] = True
        
        notes = input("Add notes (optional): ")
        if notes:
            contact["notes"] = notes
        
        # Save
        with open('contacts.json', 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"✅ Updated {contact['name']}!")
        
    except (ValueError, IndexError):
        print("Invalid contact number!")

def main():
    """Main tracking interface"""
    print("🎯 CasinoX Outreach Tracker")
    print("=" * 30)
    
    while True:
        print("\nOptions:")
        print("1. Add new contact")
        print("2. View all contacts") 
        print("3. Update contact")
        print("4. Exit")
        
        choice = input("\nChoose (1-4): ")
        
        if choice == "1":
            add_new_contact()
        elif choice == "2":
            view_contacts()
        elif choice == "3":
            update_contact()
        elif choice == "4":
            print("Good luck getting customers! 🚀")
            break
        else:
            print("Invalid choice!")

if __name__ == '__main__':
    main()
