<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ streamer.display_name }} - CasinoX{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: {{ streamer.primary_color|default:"#6366f1" }};
            --secondary-color: {{ streamer.secondary_color|default:"#8b5cf6" }};
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar-brand {
            font-weight: 700;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .game-card {
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        
        .game-card:hover {
            transform: translateY(-5px);
        }
        
        .points-display {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .leaderboard-item {
            transition: background-color 0.3s ease;
        }
        
        .leaderboard-item:hover {
            background-color: rgba(var(--primary-color), 0.1);
        }
        
        .notification {
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .wheel-container {
            position: relative;
            width: 300px;
            height: 300px;
            margin: 0 auto;
        }
        
        .wheel {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 5px solid var(--primary-color);
            position: relative;
            overflow: hidden;
            transition: transform 3s cubic-bezier(0.23, 1, 0.32, 1);
        }
        
        .wheel-pointer {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 30px solid #ff4444;
            z-index: 10;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-dice"></i> CasinoX
            </a>
            
            <div class="navbar-nav ms-auto">
                <div id="user-info" class="nav-item">
                    <!-- User info will be populated by JavaScript -->
                </div>
                
                <button id="discord-login" class="btn btn-primary ms-2" style="display: none;">
                    <i class="fab fa-discord"></i> Login with Discord
                </button>
            </div>
        </div>
    </nav>

    <!-- Streamer Header -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-body text-center">
                        {% if streamer.banner %}
                            <img src="{{ streamer.banner.url }}" alt="Banner" class="img-fluid rounded mb-3" style="max-height: 200px;">
                        {% endif %}
                        
                        <div class="d-flex align-items-center justify-content-center mb-3">
                            {% if streamer.avatar %}
                                <img src="{{ streamer.avatar.url }}" alt="Avatar" class="rounded-circle me-3" width="80" height="80">
                            {% endif %}
                            <div>
                                <h1 class="h2 mb-0">{{ streamer.display_name }}</h1>
                                <p class="text-muted mb-0">@{{ streamer.kick_channel }}</p>
                            </div>
                        </div>
                        
                        {% if streamer.bio %}
                            <p class="lead">{{ streamer.bio }}</p>
                        {% endif %}
                        
                        <div id="verification-status">
                            <!-- Verification status will be populated by JavaScript -->
                        </div>
                        
                        <button id="generate-code" class="btn btn-warning" style="display: none;">
                            <i class="fas fa-key"></i> Generate Verification Code
                        </button>
                        
                        <button id="claim-daily-reward" class="btn btn-success" style="display: none;">
                            <i class="fas fa-gift"></i> Claim Daily Reward (50 points)
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        {% block content %}{% endblock %}
    </div>

    <!-- Verification Modal -->
    <div class="modal fade" id="verification-modal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Verification Code</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="verification-instructions">
                        <!-- Instructions will be populated by JavaScript -->
                    </div>
                    <div class="text-center">
                        <div class="display-4 fw-bold text-primary" id="verification-code-display">
                            <!-- Code will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/static/js/viewer.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
