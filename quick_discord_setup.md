# Quick Discord OAuth Setup (5 Minutes)

## Step-by-Step:

1. **Go to**: https://discord.com/developers/applications
2. **Click**: "New Application"
3. **Name**: "CasinoX"
4. **Click**: "Create"
5. **Go to**: "OAuth2" → "General"
6. **Add Redirect**: `http://localhost:8000/auth/discord/callback/`
7. **Copy Client ID**: (long number)
8. **Copy Client Secret**: (click "Reset Secret" if needed)
9. **Update .env file**:
   ```
   DISCORD_CLIENT_ID=your_actual_client_id_here
   DISCORD_CLIENT_SECRET=your_actual_client_secret_here
   ```
10. **Restart server**: `python manage.py runserver`
11. **Test**: Go to http://localhost:8000/s/teststreamer/ and click "Login with Discord"

## That's it! Your platform is now 100% functional.
