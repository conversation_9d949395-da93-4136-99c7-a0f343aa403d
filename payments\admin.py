from django.contrib import admin
from .models import Payment, CryptoPayment, StripePayment


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    """Admin interface for payments"""
    list_display = [
        'payment_id', 'user', 'amount', 'currency', 'payment_method',
        'status', 'subscription_plan', 'created_at', 'completed_at'
    ]
    list_filter = ['payment_method', 'status', 'currency', 'created_at']
    search_fields = ['user__username', 'payment_id', 'external_payment_id']
    readonly_fields = ['payment_id', 'created_at', 'updated_at']
    ordering = ['-created_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'subscription_plan')


@admin.register(CryptoPayment)
class CryptoPaymentAdmin(admin.ModelAdmin):
    """Admin interface for crypto payments"""
    list_display = [
        'get_payment_id', 'get_user', 'cryptocurrency', 'crypto_amount',
        'wallet_address', 'nowpayments_id', 'created_at'
    ]
    list_filter = ['cryptocurrency', 'created_at']
    search_fields = ['payment__payment_id', 'payment__user__username', 'nowpayments_id']
    readonly_fields = ['created_at']

    def get_payment_id(self, obj):
        return obj.payment.payment_id
    get_payment_id.short_description = 'Payment ID'

    def get_user(self, obj):
        return obj.payment.user.username
    get_user.short_description = 'User'


@admin.register(StripePayment)
class StripePaymentAdmin(admin.ModelAdmin):
    """Admin interface for Stripe payments"""
    list_display = [
        'get_payment_id', 'get_user', 'stripe_payment_intent_id',
        'stripe_customer_id', 'created_at'
    ]
    list_filter = ['created_at']
    search_fields = [
        'payment__payment_id', 'payment__user__username',
        'stripe_payment_intent_id', 'stripe_customer_id'
    ]
    readonly_fields = ['created_at']

    def get_payment_id(self, obj):
        return obj.payment.payment_id
    get_payment_id.short_description = 'Payment ID'

    def get_user(self, obj):
        return obj.payment.user.username
    get_user.short_description = 'User'
