from rest_framework import serializers
from django.contrib.auth import authenticate
from .models import User, PointsTransaction


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model"""

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'user_type', 'discord_id',
            'discord_username', 'discord_avatar', 'kick_username',
            'points', 'total_wins', 'total_games_played', 'is_verified',
            'created_at', 'can_claim_daily_reward'
        ]
        read_only_fields = ['id', 'created_at', 'can_claim_daily_reward']

    def get_can_claim_daily_reward(self, obj):
        return obj.can_claim_daily_reward()


class LoginSerializer(serializers.Serializer):
    """Serializer for user login"""
    username = serializers.CharField()
    password = serializers.CharField()

    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')

        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError('Invalid credentials')
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled')
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError('Must include username and password')


class DiscordAuthSerializer(serializers.Serializer):
    """Serializer for Discord OAuth authentication"""
    code = serializers.CharField()
    state = serializers.CharField(required=False)


# VerificationCodeSerializer moved to streamers app


class GenerateCodeSerializer(serializers.Serializer):
    """Serializer for generating verification codes"""
    streamer_username = serializers.CharField()


class VerifyCodeSerializer(serializers.Serializer):
    """Serializer for verifying codes in chat"""
    code = serializers.CharField(max_length=10)
    kick_username = serializers.CharField(max_length=100)
    streamer_username = serializers.CharField(max_length=100)


class PointsTransactionSerializer(serializers.ModelSerializer):
    """Serializer for points transactions"""

    class Meta:
        model = PointsTransaction
        fields = [
            'id', 'amount', 'transaction_type', 'reason', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class DailyRewardSerializer(serializers.Serializer):
    """Serializer for claiming daily rewards"""
    pass


class UserStatsSerializer(serializers.ModelSerializer):
    """Serializer for user statistics"""
    rank = serializers.SerializerMethodField()
    win_rate = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'username', 'points', 'total_wins', 'total_games_played',
            'rank', 'win_rate', 'discord_avatar'
        ]

    def get_rank(self, obj):
        """Get user's rank based on points"""
        return User.objects.filter(points__gt=obj.points).count() + 1

    def get_win_rate(self, obj):
        """Calculate win rate percentage"""
        if obj.total_games_played == 0:
            return 0
        return round((obj.total_wins / obj.total_games_played) * 100, 2)
