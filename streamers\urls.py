from django.urls import path
from . import views

urlpatterns = [
    # Streamer Dashboard & Profile
    path('dashboard/', views.StreamerDashboardView.as_view(), name='streamer_dashboard'),
    path('profile/', views.StreamerProfileView.as_view(), name='streamer_profile'),
    
    # Wheel Configuration
    path('wheel/', views.WheelConfigurationView.as_view(), name='wheel_config'),
    
    # Shop Management
    path('shop/', views.ShopConfigurationView.as_view(), name='shop_config'),
    path('shop/items/', views.ShopItemListCreateView.as_view(), name='shop_items'),
    path('shop/items/<int:pk>/', views.ShopItemDetailView.as_view(), name='shop_item_detail'),
    path('shop/purchases/', views.ShopPurchaseListView.as_view(), name='shop_purchases'),
    
    # Chat Commands
    path('commands/', views.ChatCommandListCreateView.as_view(), name='chat_commands'),
    path('commands/<int:pk>/', views.ChatCommandDetailView.as_view(), name='chat_command_detail'),
    
    # OBS Overlays
    path('overlays/', views.OBSOverlayListCreateView.as_view(), name='obs_overlays'),
    path('overlays/<int:pk>/', views.OBSOverlayDetailView.as_view(), name='obs_overlay_detail'),
    
    # Subscription Plans
    path('plans/', views.SubscriptionPlanListView.as_view(), name='subscription_plans'),
]
