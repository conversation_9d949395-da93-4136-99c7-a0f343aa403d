from django.db import models
from django.contrib.auth import get_user_model
import uuid

User = get_user_model()


class Payment(models.Model):
    """Payment records for subscriptions"""
    PAYMENT_METHODS = (
        ('crypto', 'Cryptocurrency'),
        ('stripe', 'Credit Card (Stripe)'),
    )
    
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    )
    
    payment_id = models.UUIDField(default=uuid.uuid4, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Payment details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=10, default='USD')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS)
    status = models.Char<PERSON>ield(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # External payment data
    external_payment_id = models.CharField(max_length=200, blank=True)
    payment_data = models.JSONField(default=dict)
    
    # Subscription info
    subscription_plan = models.ForeignKey('streamers.SubscriptionPlan', on_delete=models.SET_NULL, null=True)
    subscription_months = models.IntegerField(default=1)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Payment {self.payment_id} - {self.user.username}"


class CryptoPayment(models.Model):
    """Crypto payment details"""
    payment = models.OneToOneField(Payment, on_delete=models.CASCADE)
    
    # Crypto details
    cryptocurrency = models.CharField(max_length=20)  # BTC, USDT, BNB, etc.
    crypto_amount = models.DecimalField(max_digits=20, decimal_places=8)
    wallet_address = models.CharField(max_length=200)
    
    # NOWPayments data
    nowpayments_id = models.CharField(max_length=100, blank=True)
    payment_url = models.URLField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Crypto payment {self.cryptocurrency} - {self.crypto_amount}"


class StripePayment(models.Model):
    """Stripe payment details"""
    payment = models.OneToOneField(Payment, on_delete=models.CASCADE)
    
    # Stripe data
    stripe_payment_intent_id = models.CharField(max_length=200)
    stripe_customer_id = models.CharField(max_length=200, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Stripe payment {self.stripe_payment_intent_id}"
