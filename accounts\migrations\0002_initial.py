# Generated by Django 4.2.7 on 2025-07-18 20:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
        ("accounts", "0001_initial"),
        ("games", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="pointstransaction",
            name="game_session",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="games.gamesession",
            ),
        ),
        migrations.AddField(
            model_name="pointstransaction",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="groups",
            field=models.ManyToManyField(
                blank=True,
                help_text="The groups this user belongs to.",
                related_name="casinox_users",
                related_query_name="casinox_user",
                to="auth.group",
                verbose_name="groups",
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="user_permissions",
            field=models.ManyToManyField(
                blank=True,
                help_text="Specific permissions for this user.",
                related_name="casinox_users",
                related_query_name="casinox_user",
                to="auth.permission",
                verbose_name="user permissions",
            ),
        ),
    ]
