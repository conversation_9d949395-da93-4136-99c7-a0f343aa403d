from django.contrib import admin
from .models import (
    KickChatConnection, ChatMessage, CommandCooldown,
    BotResponse, ChatAnalytics, WebSocketConnection
)


@admin.register(KickChatConnection)
class KickChatConnectionAdmin(admin.ModelAdmin):
    """Admin interface for Kick chat connections"""
    list_display = [
        'streamer', 'kick_channel_id', 'is_connected', 'is_active',
        'bot_username', 'total_messages_processed', 'error_count',
        'last_connected'
    ]
    list_filter = ['is_connected', 'is_active', 'created_at']
    search_fields = ['streamer__display_name', 'kick_channel_id', 'bot_username']
    readonly_fields = [
        'last_connected', 'last_disconnected', 'total_messages_processed',
        'error_count', 'created_at', 'updated_at'
    ]


@admin.register(ChatMessage)
class ChatMessageAdmin(admin.ModelAdmin):
    """Admin interface for chat messages"""
    list_display = [
        'username', 'content', 'message_type', 'is_processed',
        'connection', 'timestamp'
    ]
    list_filter = ['message_type', 'is_processed', 'timestamp']
    search_fields = ['username', 'content', 'user_id']
    readonly_fields = ['kick_message_id', 'timestamp', 'created_at', 'processed_at']
    ordering = ['-timestamp']

    def get_queryset(self, request):
        # Limit to recent messages for performance
        from django.utils import timezone
        from datetime import timedelta
        
        qs = super().get_queryset(request)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        return qs.filter(timestamp__gte=thirty_days_ago)


@admin.register(CommandCooldown)
class CommandCooldownAdmin(admin.ModelAdmin):
    """Admin interface for command cooldowns"""
    list_display = ['username', 'command', 'connection', 'last_used']
    list_filter = ['command', 'last_used']
    search_fields = ['username', 'command']


@admin.register(BotResponse)
class BotResponseAdmin(admin.ModelAdmin):
    """Admin interface for bot responses"""
    list_display = [
        'get_username', 'response_type', 'was_sent', 'created_at'
    ]
    list_filter = ['response_type', 'was_sent', 'created_at']
    search_fields = ['original_message__username', 'response_text']
    readonly_fields = ['created_at']

    def get_username(self, obj):
        return obj.original_message.username
    get_username.short_description = 'Username'


@admin.register(ChatAnalytics)
class ChatAnalyticsAdmin(admin.ModelAdmin):
    """Admin interface for chat analytics"""
    list_display = [
        'connection', 'date', 'total_messages', 'unique_users',
        'commands_used', 'verifications_successful'
    ]
    list_filter = ['date', 'connection__streamer']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-date']


@admin.register(WebSocketConnection)
class WebSocketConnectionAdmin(admin.ModelAdmin):
    """Admin interface for WebSocket connections"""
    list_display = [
        'connection_id', 'get_username', 'streamer', 'connection_type',
        'is_active', 'connected_at', 'last_activity'
    ]
    list_filter = ['connection_type', 'is_active', 'connected_at']
    search_fields = ['user__username', 'streamer__display_name', 'ip_address']
    readonly_fields = ['connection_id', 'connected_at', 'last_activity', 'disconnected_at']

    def get_username(self, obj):
        return obj.user.username if obj.user else 'Anonymous'
    get_username.short_description = 'User'
