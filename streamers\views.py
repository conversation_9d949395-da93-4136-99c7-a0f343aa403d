from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone

from .models import (
    Streamer, SubscriptionPlan, WheelConfiguration,
    ShopConfiguration, ShopItem, ShopPurchase,
    ChatCommand, OBSOverlay
)
from .serializers import (
    StreamerSerializer, StreamerPublicSerializer, StreamerDashboardSerializer,
    WheelConfigurationSerializer, ShopConfigurationSerializer,
    ShopItemSerializer, ShopPurchaseSerializer, CreatePurchaseSerializer,
    ChatCommandSerializer, OBSOverlaySerializer, SubscriptionPlanSerializer
)
from accounts.models import User


class IsStreamerOrReadOnly(permissions.BasePermission):
    """Custom permission to only allow streamers to edit their own data"""
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for any request
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions only for the streamer themselves
        if hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'streamer'):
            return obj.streamer.user == request.user
        return False


class StreamerDashboardView(generics.RetrieveUpdateAPIView):
    """Streamer dashboard - comprehensive view of all settings"""
    serializer_class = StreamerDashboardSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        try:
            return Streamer.objects.get(user=self.request.user)
        except Streamer.DoesNotExist:
            # Create streamer profile if it doesn't exist
            return Streamer.objects.create(
                user=self.request.user,
                kick_channel=self.request.user.username,
                display_name=self.request.user.username
            )


class StreamerProfileView(generics.RetrieveUpdateAPIView):
    """Streamer profile management"""
    serializer_class = StreamerSerializer
    permission_classes = [permissions.IsAuthenticated, IsStreamerOrReadOnly]

    def get_object(self):
        return get_object_or_404(Streamer, user=self.request.user)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def streamer_public_view(request, streamer_username):
    """Public view of streamer for viewers"""
    try:
        streamer = Streamer.objects.get(
            user__username=streamer_username,
            is_active=True
        )
        serializer = StreamerPublicSerializer(streamer)
        return Response(serializer.data)
    except Streamer.DoesNotExist:
        return Response(
            {'error': 'Streamer not found'},
            status=status.HTTP_404_NOT_FOUND
        )


class WheelConfigurationView(generics.RetrieveUpdateAPIView):
    """Wheel configuration management"""
    serializer_class = WheelConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        wheel_config, created = WheelConfiguration.objects.get_or_create(
            streamer=streamer,
            defaults={
                'prizes': [
                    {'name': '10 Points', 'value': 10, 'probability': 30},
                    {'name': '25 Points', 'value': 25, 'probability': 25},
                    {'name': '50 Points', 'value': 50, 'probability': 20},
                    {'name': '100 Points', 'value': 100, 'probability': 15},
                    {'name': '250 Points', 'value': 250, 'probability': 8},
                    {'name': '500 Points', 'value': 500, 'probability': 2},
                ],
                'wheel_colors': ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3']
            }
        )
        return wheel_config


class ShopConfigurationView(generics.RetrieveUpdateAPIView):
    """Shop configuration management"""
    serializer_class = ShopConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        shop_config, created = ShopConfiguration.objects.get_or_create(
            streamer=streamer
        )
        return shop_config


class ShopItemListCreateView(generics.ListCreateAPIView):
    """List and create shop items"""
    serializer_class = ShopItemSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        shop_config, _ = ShopConfiguration.objects.get_or_create(streamer=streamer)
        return shop_config.items.all()

    def perform_create(self, serializer):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        shop_config, _ = ShopConfiguration.objects.get_or_create(streamer=streamer)
        serializer.save(shop=shop_config)


class ShopItemDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete shop items"""
    serializer_class = ShopItemSerializer
    permission_classes = [permissions.IsAuthenticated, IsStreamerOrReadOnly]

    def get_queryset(self):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        shop_config, _ = ShopConfiguration.objects.get_or_create(streamer=streamer)
        return shop_config.items.all()


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def purchase_shop_item(request, streamer_username):
    """Purchase an item from streamer's shop"""
    serializer = CreatePurchaseSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    try:
        streamer = Streamer.objects.get(user__username=streamer_username)
        item = ShopItem.objects.get(
            id=serializer.validated_data['item_id'],
            shop__streamer=streamer,
            is_available=True
        )
    except (Streamer.DoesNotExist, ShopItem.DoesNotExist):
        return Response(
            {'error': 'Item not found or not available'},
            status=status.HTTP_404_NOT_FOUND
        )

    quantity = serializer.validated_data['quantity']
    total_price = item.price * quantity

    # Check if user has enough points
    if request.user.points < total_price:
        return Response(
            {'error': 'Insufficient points'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Check stock
    if item.stock_quantity is not None and item.stock_quantity < quantity:
        return Response(
            {'error': 'Insufficient stock'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Check max per user limit
    if item.max_per_user:
        user_purchases = ShopPurchase.objects.filter(
            item=item,
            user=request.user,
            status='completed'
        ).aggregate(total=models.Sum('quantity'))['total'] or 0
        
        if user_purchases + quantity > item.max_per_user:
            return Response(
                {'error': f'Maximum {item.max_per_user} per user'},
                status=status.HTTP_400_BAD_REQUEST
            )

    # Process purchase
    with transaction.atomic():
        # Deduct points
        if not request.user.deduct_points(total_price, f"Shop purchase: {item.name}"):
            return Response(
                {'error': 'Failed to deduct points'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create purchase record
        purchase = ShopPurchase.objects.create(
            item=item,
            user=request.user,
            quantity=quantity,
            total_price=total_price,
            status='completed'
        )

        # Update stock
        if item.stock_quantity is not None:
            item.stock_quantity -= quantity
            item.save()

    return Response({
        'message': f'Successfully purchased {quantity}x {item.name}',
        'purchase_id': purchase.purchase_id,
        'remaining_points': request.user.points
    })


class ShopPurchaseListView(generics.ListAPIView):
    """List shop purchases for streamer"""
    serializer_class = ShopPurchaseSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        return ShopPurchase.objects.filter(
            item__shop__streamer=streamer
        ).order_by('-created_at')


class ChatCommandListCreateView(generics.ListCreateAPIView):
    """List and create chat commands"""
    serializer_class = ChatCommandSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        return streamer.chat_commands.all()

    def perform_create(self, serializer):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        serializer.save(streamer=streamer)


class ChatCommandDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete chat commands"""
    serializer_class = ChatCommandSerializer
    permission_classes = [permissions.IsAuthenticated, IsStreamerOrReadOnly]

    def get_queryset(self):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        return streamer.chat_commands.all()


class OBSOverlayListCreateView(generics.ListCreateAPIView):
    """List and create OBS overlays"""
    serializer_class = OBSOverlaySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        return streamer.obs_overlays.all()

    def perform_create(self, serializer):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        serializer.save(streamer=streamer)


class OBSOverlayDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete OBS overlays"""
    serializer_class = OBSOverlaySerializer
    permission_classes = [permissions.IsAuthenticated, IsStreamerOrReadOnly]

    def get_queryset(self):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        return streamer.obs_overlays.all()


class SubscriptionPlanListView(generics.ListAPIView):
    """List available subscription plans"""
    serializer_class = SubscriptionPlanSerializer
    permission_classes = [permissions.AllowAny]
    queryset = SubscriptionPlan.objects.filter(is_active=True)
