#!/bin/bash
# Quick deployment script for CasinoX production

echo "🚀 CasinoX Production Deployment"
echo "================================"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "Please run as root (use sudo)"
    exit 1
fi

# Update system
echo "📦 Updating system..."
apt update && apt upgrade -y

# Install Docker
echo "🐳 Installing Docker..."
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
apt install docker-compose-plugin -y

# Install other dependencies
echo "📋 Installing dependencies..."
apt install git nginx certbot python3-certbot-nginx ufw -y

# Configure firewall
echo "🔥 Configuring firewall..."
ufw allow ssh
ufw allow http
ufw allow https
ufw --force enable

# Create app directory
echo "📁 Setting up directories..."
mkdir -p /var/www/casinox
cd /var/www/casinox

# Clone repository (you'll need to update this with your actual repo)
echo "📥 Cloning repository..."
echo "⚠️  Update this with your actual repository URL"
# git clone https://github.com/yourusername/casinox.git .

# Set up environment
echo "⚙️ Setting up environment..."
if [ ! -f .env ]; then
    cp .env.production .env
    echo "📝 Please edit .env file with your production values"
    echo "   nano .env"
fi

# Build and start containers
echo "🏗️ Building containers..."
docker compose -f docker-compose.prod.yml build

echo "🚀 Starting services..."
docker compose -f docker-compose.prod.yml up -d

# Run migrations
echo "🗄️ Running database migrations..."
docker compose -f docker-compose.prod.yml exec web python manage.py migrate

# Collect static files
echo "📦 Collecting static files..."
docker compose -f docker-compose.prod.yml exec web python manage.py collectstatic --noinput

# Create superuser
echo "👤 Creating admin user..."
docker compose -f docker-compose.prod.yml exec web python manage.py create_superuser --username admin --email <EMAIL> --password admin123

# Setup subscription plans
echo "💳 Setting up subscription plans..."
docker compose -f docker-compose.prod.yml exec web python manage.py setup_subscription_plans

echo ""
echo "✅ Deployment completed!"
echo ""
echo "Next steps:"
echo "1. Update .env with your production values"
echo "2. Point your domain to this server"
echo "3. Run: certbot --nginx -d yourdomain.com"
echo "4. Test your site: https://yourdomain.com"
echo ""
echo "🎉 CasinoX is ready for production!"
