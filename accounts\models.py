from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
import uuid
import secrets
import string


class User(AbstractUser):
    """Extended User model for CasinoX platform"""
    USER_TYPES = (
        ('admin', 'Platform Admin'),
        ('streamer', 'Streamer'),
        ('viewer', 'Viewer'),
    )
    
    user_type = models.CharField(max_length=20, choices=USER_TYPES, default='viewer')
    discord_id = models.CharField(max_length=50, unique=True, null=True, blank=True)
    discord_username = models.CharField(max_length=100, null=True, blank=True)
    discord_avatar = models.URLField(null=True, blank=True)
    kick_username = models.CharField(max_length=100, null=True, blank=True)
    points = models.IntegerField(default=0)
    total_wins = models.IntegerField(default=0)
    total_games_played = models.IntegerField(default=0)
    last_daily_reward = models.DateTimeField(null=True, blank=True)
    is_verified = models.Bo<PERSON>anField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.username} ({self.user_type})"

    def can_claim_daily_reward(self):
        """Check if user can claim daily reward"""
        if not self.last_daily_reward:
            return True
        return (timezone.now() - self.last_daily_reward).days >= 1

    def add_points(self, amount, reason=""):
        """Add points to user account"""
        self.points += amount
        self.save()
        
        # Log the transaction
        PointsTransaction.objects.create(
            user=self,
            amount=amount,
            transaction_type='credit',
            reason=reason
        )

    def deduct_points(self, amount, reason=""):
        """Deduct points from user account"""
        if self.points >= amount:
            self.points -= amount
            self.save()
            
            # Log the transaction
            PointsTransaction.objects.create(
                user=self,
                amount=amount,
                transaction_type='debit',
                reason=reason
            )
            return True
        return False


class VerificationCode(models.Model):
    """Verification codes for linking Discord to Kick chat"""
    code = models.CharField(max_length=10, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    streamer = models.ForeignKey('streamers.Streamer', on_delete=models.CASCADE)
    is_used = models.BooleanField(default=False)
    expires_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    used_at = models.DateTimeField(null=True, blank=True)
    kick_username = models.CharField(max_length=100, null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    def __str__(self):
        return f"Code {self.code} for {self.user.username}"

    @classmethod
    def generate_code(cls, user, streamer, ip_address=None):
        """Generate a new verification code"""
        # Generate random 6-character alphanumeric code
        code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(6))
        
        # Ensure uniqueness
        while cls.objects.filter(code=code).exists():
            code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(6))
        
        # Set expiration to 5 minutes from now
        expires_at = timezone.now() + timezone.timedelta(minutes=5)
        
        return cls.objects.create(
            code=code,
            user=user,
            streamer=streamer,
            expires_at=expires_at,
            ip_address=ip_address
        )

    def is_expired(self):
        """Check if code is expired"""
        return timezone.now() > self.expires_at

    def use_code(self, kick_username):
        """Mark code as used"""
        if self.is_used or self.is_expired():
            return False
        
        self.is_used = True
        self.used_at = timezone.now()
        self.kick_username = kick_username
        self.save()
        
        # Update user's kick username and verification status
        self.user.kick_username = kick_username
        self.user.is_verified = True
        self.user.save()
        
        return True


class PointsTransaction(models.Model):
    """Track all points transactions"""
    TRANSACTION_TYPES = (
        ('credit', 'Credit'),
        ('debit', 'Debit'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    amount = models.IntegerField()
    transaction_type = models.CharField(max_length=10, choices=TRANSACTION_TYPES)
    reason = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)
    game_session = models.ForeignKey('games.GameSession', on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username}: {self.transaction_type} {self.amount} points"


class UserSession(models.Model):
    """Track user sessions for analytics"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    session_id = models.UUIDField(default=uuid.uuid4, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    streamer = models.ForeignKey('streamers.Streamer', on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return f"Session {self.session_id} for {self.user.username}"
