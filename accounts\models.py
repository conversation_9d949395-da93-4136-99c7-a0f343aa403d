from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
import uuid
import secrets
import string


class User(AbstractUser):
    """Extended User model for CasinoX platform"""
    USER_TYPES = (
        ('admin', 'Platform Admin'),
        ('streamer', 'Streamer'),
        ('viewer', 'Viewer'),
    )

    user_type = models.CharField(max_length=20, choices=USER_TYPES, default='viewer')
    discord_id = models.CharField(max_length=50, unique=True, null=True, blank=True)
    discord_username = models.CharField(max_length=100, null=True, blank=True)
    discord_avatar = models.URLField(null=True, blank=True)
    kick_username = models.CharField(max_length=100, null=True, blank=True)
    points = models.IntegerField(default=0)
    total_wins = models.IntegerField(default=0)
    total_games_played = models.IntegerField(default=0)
    last_daily_reward = models.DateTimeField(null=True, blank=True)
    is_verified = models.Bo<PERSON>an<PERSON>ield(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Fix the reverse accessor conflicts
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text='The groups this user belongs to.',
        related_name='casinox_users',
        related_query_name='casinox_user',
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name='casinox_users',
        related_query_name='casinox_user',
    )

    def __str__(self):
        return f"{self.username} ({self.user_type})"

    def can_claim_daily_reward(self):
        """Check if user can claim daily reward"""
        if not self.last_daily_reward:
            return True
        return (timezone.now() - self.last_daily_reward).days >= 1

    def add_points(self, amount, reason=""):
        """Add points to user account"""
        self.points += amount
        self.save()

        # Log the transaction
        PointsTransaction.objects.create(
            user=self,
            amount=amount,
            transaction_type='credit',
            reason=reason
        )

    def deduct_points(self, amount, reason=""):
        """Deduct points from user account"""
        if self.points >= amount:
            self.points -= amount
            self.save()

            # Log the transaction
            PointsTransaction.objects.create(
                user=self,
                amount=amount,
                transaction_type='debit',
                reason=reason
            )
            return True
        return False


# VerificationCode moved to streamers app to avoid circular dependency


class PointsTransaction(models.Model):
    """Track all points transactions"""
    TRANSACTION_TYPES = (
        ('credit', 'Credit'),
        ('debit', 'Debit'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    amount = models.IntegerField()
    transaction_type = models.CharField(max_length=10, choices=TRANSACTION_TYPES)
    reason = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)
    game_session = models.ForeignKey('games.GameSession', on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username}: {self.transaction_type} {self.amount} points"


# UserSession moved to streamers app to avoid circular dependency
