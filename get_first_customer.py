#!/usr/bin/env python3
"""
Get your first CasinoX customer - Step by step guide
"""

import json
import os
from datetime import datetime

def check_discord_oauth():
    """Check if Discord OAuth is set up"""
    print("🔐 Checking Discord OAuth Setup")
    print("=" * 50)
    
    from decouple import config
    client_id = config('DISCORD_CLIENT_ID', default='')
    
    if client_id and client_id not in ['your-discord-client-id', 'PASTE_YOUR_CLIENT_ID_HERE']:
        print("✅ Discord OAuth is configured!")
        print(f"   Client ID: {client_id}")
        return True
    else:
        print("❌ Discord OAuth NOT configured")
        print("   📋 Action: Follow quick_discord_setup.md")
        return False


def create_first_customer_checklist():
    """Create checklist for getting first customer"""
    print(f"\n📋 First Customer Checklist")
    print("=" * 50)
    
    checklist = {
        "setup_tasks": [
            {"task": "Set up Discord OAuth", "completed": False, "time": "5 min"},
            {"task": "Test platform functionality", "completed": False, "time": "10 min"},
            {"task": "Create demo video/screenshots", "completed": False, "time": "30 min"}
        ],
        "research_tasks": [
            {"task": "Find 10 Kick casino streamers", "completed": False, "time": "30 min"},
            {"task": "Join their Discord servers", "completed": False, "time": "20 min"},
            {"task": "Watch their streams to understand needs", "completed": False, "time": "60 min"}
        ],
        "outreach_tasks": [
            {"task": "Send 5 personalized Discord DMs", "completed": False, "time": "45 min"},
            {"task": "Follow up with interested prospects", "completed": False, "time": "30 min"},
            {"task": "Schedule demo calls", "completed": False, "time": "15 min"}
        ],
        "conversion_tasks": [
            {"task": "Conduct demo presentation", "completed": False, "time": "30 min"},
            {"task": "Set up trial account", "completed": False, "time": "10 min"},
            {"task": "Provide onboarding support", "completed": False, "time": "60 min"},
            {"task": "Convert trial to paid", "completed": False, "time": "15 min"}
        ]
    }
    
    with open('first_customer_checklist.json', 'w') as f:
        json.dump(checklist, f, indent=2)
    
    print("✅ Created first_customer_checklist.json")
    
    total_time = 0
    for category, tasks in checklist.items():
        print(f"\n📂 {category.replace('_', ' ').title()}:")
        for task in tasks:
            status = "✅" if task["completed"] else "⏳"
            print(f"   {status} {task['task']} ({task['time']})")
            total_time += int(task['time'].split()[0])
    
    print(f"\n⏱️ Total estimated time: {total_time} minutes ({total_time/60:.1f} hours)")


def create_demo_script():
    """Create demo presentation script"""
    print(f"\n🎥 Demo Presentation Script")
    print("=" * 50)
    
    demo_script = """
# CasinoX Demo Script (5-10 minutes)

## Opening (30 seconds)
"Hi [Name]! Thanks for your interest in CasinoX. I'm going to show you exactly how this can transform your stream engagement. This will take about 5 minutes."

## Problem Statement (30 seconds)
"I noticed casino streamers like yourself have viewers who mostly just watch passively. Chat engagement is low, and it's hard to build a real community around your content."

## Solution Demo (3-4 minutes)

### 1. Show Streamer Page
- "Here's what your viewers would see: [show demo streamer page]"
- "They can login with Discord in one click"
- "Generate verification codes to link Discord to Kick chat"

### 2. Show Interactive Features
- "Watch this - they can spin the wheel and win real prizes"
- "Play mini-games like slots and dice"
- "Earn points and spend them in your custom shop"

### 3. Show Real-time Updates
- "Everything updates in real-time across all viewers"
- "Leaderboards create competition"
- "OBS overlays show results on your stream"

### 4. Show Admin Dashboard
- "You control everything from this dashboard"
- "Customize wheel prizes, shop items, chat commands"
- "See detailed analytics on engagement"

## Results (1 minute)
"Our beta streamers saw:
- 40% increase in viewer engagement
- 3x more chat activity  
- Higher subscriber conversion rates
- More time spent on stream"

## Call to Action (30 seconds)
"I can set you up with a free trial right now. Takes 2 minutes to configure for your channel. Want to give it a try?"

## Objection Handling
- "Too complicated?" → "Setup takes 2 minutes, I'll walk you through it"
- "Too expensive?" → "Free 7-day trial, cancel anytime, costs less than one subscriber"
- "Not sure viewers will use it?" → "Our data shows 60% of viewers participate within first stream"

## Closing
"Great! Let me get you set up. What's your Kick channel name?"
"""
    
    with open('demo_script.md', 'w') as f:
        f.write(demo_script)
    
    print("✅ Created demo_script.md")


def track_progress():
    """Track progress towards first customer"""
    print(f"\n📊 Progress Tracking")
    print("=" * 50)
    
    progress_tracker = {
        "goal": "Get first paying customer",
        "target_date": "Within 7 days",
        "current_status": "Setup phase",
        "metrics": {
            "prospects_identified": 0,
            "messages_sent": 0,
            "responses_received": 0,
            "demos_scheduled": 0,
            "trials_started": 0,
            "customers_converted": 0
        },
        "daily_goals": {
            "day_1": "Set up Discord OAuth + find 10 prospects",
            "day_2": "Send 5 outreach messages",
            "day_3": "Follow up + send 5 more messages", 
            "day_4": "Schedule and conduct demos",
            "day_5": "Set up trials + provide support",
            "day_6": "Follow up on trials",
            "day_7": "Convert first customer!"
        },
        "last_updated": datetime.now().isoformat()
    }
    
    with open('progress_tracker.json', 'w') as f:
        json.dump(progress_tracker, f, indent=2)
    
    print("✅ Created progress_tracker.json")
    print(f"🎯 Goal: {progress_tracker['goal']}")
    print(f"📅 Target: {progress_tracker['target_date']}")
    
    print(f"\n📈 Daily Goals:")
    for day, goal in progress_tracker['daily_goals'].items():
        print(f"   {day.replace('_', ' ').title()}: {goal}")


def print_immediate_next_steps():
    """Print what to do right now"""
    print(f"\n🚀 DO THIS RIGHT NOW (Next 30 minutes)")
    print("=" * 50)
    
    steps = [
        "1. 🔐 Set up Discord OAuth (5 min)",
        "   → Open: https://discord.com/developers/applications",
        "   → Follow: quick_discord_setup.md",
        "",
        "2. 🔍 Find 3 target streamers (15 min)",
        "   → Go to: kick.com",
        "   → Search: 'casino', 'slots', 'gambling'",
        "   → Find streamers with 50-500 viewers",
        "   → Add to customer_prospects.json",
        "",
        "3. 📧 Send first message (10 min)",
        "   → Pick highest priority prospect",
        "   → Use pitch from pitch_variations.json",
        "   → Send Discord DM or Twitter message",
        "   → Track in outreach_tracker.json"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print(f"\n🎯 SUCCESS CRITERIA:")
    print(f"   ✅ Discord OAuth working (test login)")
    print(f"   ✅ 3 prospects identified and researched")
    print(f"   ✅ 1 outreach message sent")
    print(f"   ✅ Progress tracked in JSON files")
    
    print(f"\n💰 POTENTIAL OUTCOME:")
    print(f"   • 1 message → 20% response rate → 1 response")
    print(f"   • 1 response → 50% demo rate → 1 demo")
    print(f"   • 1 demo → 100% trial rate → 1 trial")
    print(f"   • 1 trial → 20% conversion → $25 MRR")


def main():
    """Execute first customer acquisition plan"""
    print("🎯 Get Your First CasinoX Customer")
    print("=" * 50)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    discord_ready = check_discord_oauth()
    create_first_customer_checklist()
    create_demo_script()
    track_progress()
    print_immediate_next_steps()
    
    print(f"\n" + "=" * 50)
    if discord_ready:
        print("🎉 READY TO GET CUSTOMERS!")
        print("Your platform is functional - start reaching out!")
    else:
        print("⚠️ SET UP DISCORD OAUTH FIRST!")
        print("Then come back and start customer acquisition.")


if __name__ == '__main__':
    main()
