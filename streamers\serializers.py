from rest_framework import serializers
from .models import (
    Streamer, SubscriptionPlan, WheelConfiguration,
    ShopConfiguration, ShopItem, ShopPurchase,
    ChatCommand, OBSOverlay, VerificationCode
)


class SubscriptionPlanSerializer(serializers.ModelSerializer):
    """Serializer for subscription plans"""

    class Meta:
        model = SubscriptionPlan
        fields = [
            'id', 'name', 'display_name', 'price_monthly',
            'features', 'max_concurrent_users'
        ]


class StreamerSerializer(serializers.ModelSerializer):
    """Serializer for streamer profiles"""
    subscription_plan = SubscriptionPlanSerializer(read_only=True)
    username = serializers.CharField(source='user.username', read_only=True)

    class Meta:
        model = Streamer
        fields = [
            'id', 'username', 'kick_channel', 'display_name', 'bio',
            'avatar', 'banner', 'subscription_plan', 'subscription_active',
            'subscription_expires_at', 'is_active', 'timezone',
            'primary_color', 'secondary_color', 'wheel_enabled',
            'games_enabled', 'shop_enabled', 'leaderboard_enabled',
            'obs_overlays_enabled', 'bot_enabled', 'auto_reply_enabled',
            'command_cooldown', 'total_viewers', 'total_games_played',
            'total_points_distributed', 'created_at'
        ]
        read_only_fields = [
            'id', 'username', 'total_viewers', 'total_games_played',
            'total_points_distributed', 'created_at'
        ]


class StreamerPublicSerializer(serializers.ModelSerializer):
    """Public serializer for streamer info (for viewer pages)"""
    username = serializers.CharField(source='user.username', read_only=True)

    class Meta:
        model = Streamer
        fields = [
            'username', 'kick_channel', 'display_name', 'bio',
            'avatar', 'banner', 'primary_color', 'secondary_color',
            'wheel_enabled', 'games_enabled', 'shop_enabled',
            'leaderboard_enabled'
        ]


class WheelConfigurationSerializer(serializers.ModelSerializer):
    """Serializer for wheel configuration"""

    class Meta:
        model = WheelConfiguration
        fields = [
            'id', 'trigger_keyword', 'min_points_to_spin', 'max_points_per_spin',
            'spin_cooldown', 'auto_remove_idle', 'idle_timeout', 'prizes',
            'wheel_colors', 'show_in_obs'
        ]


class ShopItemSerializer(serializers.ModelSerializer):
    """Serializer for shop items"""

    class Meta:
        model = ShopItem
        fields = [
            'id', 'name', 'description', 'price', 'image',
            'is_available', 'stock_quantity', 'max_per_user',
            'category', 'sort_order'
        ]


class ShopConfigurationSerializer(serializers.ModelSerializer):
    """Serializer for shop configuration"""
    items = ShopItemSerializer(many=True, read_only=True)

    class Meta:
        model = ShopConfiguration
        fields = [
            'id', 'is_enabled', 'welcome_message', 'items'
        ]


class ShopPurchaseSerializer(serializers.ModelSerializer):
    """Serializer for shop purchases"""
    item_name = serializers.CharField(source='item.name', read_only=True)
    user_username = serializers.CharField(source='user.username', read_only=True)

    class Meta:
        model = ShopPurchase
        fields = [
            'id', 'purchase_id', 'item_name', 'user_username',
            'quantity', 'total_price', 'status', 'notes', 'created_at'
        ]


class CreatePurchaseSerializer(serializers.Serializer):
    """Serializer for creating shop purchases"""
    item_id = serializers.IntegerField()
    quantity = serializers.IntegerField(min_value=1, default=1)


class ChatCommandSerializer(serializers.ModelSerializer):
    """Serializer for chat commands"""

    class Meta:
        model = ChatCommand
        fields = [
            'id', 'command', 'response', 'is_enabled', 'cooldown',
            'mod_only', 'subscriber_only', 'usage_count', 'last_used'
        ]
        read_only_fields = ['usage_count', 'last_used']


class OBSOverlaySerializer(serializers.ModelSerializer):
    """Serializer for OBS overlays"""
    overlay_url = serializers.CharField(source='get_overlay_url', read_only=True)

    class Meta:
        model = OBSOverlay
        fields = [
            'id', 'overlay_type', 'name', 'settings', 'css_styles',
            'is_enabled', 'width', 'height', 'overlay_id', 'overlay_url'
        ]
        read_only_fields = ['overlay_id', 'overlay_url']


class StreamerDashboardSerializer(serializers.ModelSerializer):
    """Comprehensive serializer for streamer dashboard"""
    subscription_plan = SubscriptionPlanSerializer(read_only=True)
    wheel_config = WheelConfigurationSerializer(read_only=True)
    shop_config = ShopConfigurationSerializer(read_only=True)
    chat_commands = ChatCommandSerializer(many=True, read_only=True)
    obs_overlays = OBSOverlaySerializer(many=True, read_only=True)
    username = serializers.CharField(source='user.username', read_only=True)

    class Meta:
        model = Streamer
        fields = [
            'id', 'username', 'kick_channel', 'display_name', 'bio',
            'avatar', 'banner', 'subscription_plan', 'subscription_active',
            'subscription_expires_at', 'is_active', 'timezone',
            'primary_color', 'secondary_color', 'wheel_enabled',
            'games_enabled', 'shop_enabled', 'leaderboard_enabled',
            'obs_overlays_enabled', 'bot_enabled', 'auto_reply_enabled',
            'command_cooldown', 'total_viewers', 'total_games_played',
            'total_points_distributed', 'wheel_config', 'shop_config',
            'chat_commands', 'obs_overlays', 'created_at'
        ]


class VerificationCodeSerializer(serializers.ModelSerializer):
    """Serializer for verification codes"""

    class Meta:
        model = VerificationCode
        fields = [
            'id', 'code', 'is_used', 'expires_at', 'created_at',
            'used_at', 'kick_username'
        ]
        read_only_fields = ['id', 'created_at', 'used_at']
