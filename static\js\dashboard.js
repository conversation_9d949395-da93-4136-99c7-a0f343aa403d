// CasinoX Dashboard JavaScript

class CasinoXDashboard {
    constructor() {
        this.websocket = null;
        this.charts = {};
        this.init();
    }

    init() {
        this.setupWebSocket();
        this.setupEventListeners();
        this.loadDashboardData();
        this.setupCharts();
    }

    setupWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/dashboard/${window.streamerUsername}/`;
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            console.log('Dashboard WebSocket connected');
        };
        
        this.websocket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
        };
        
        this.websocket.onclose = () => {
            console.log('Dashboard WebSocket disconnected');
            // Attempt to reconnect after 5 seconds
            setTimeout(() => this.setupWebSocket(), 5000);
        };
        
        this.websocket.onerror = (error) => {
            console.error('Dashboard WebSocket error:', error);
        };
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'analytics_update':
                this.updateAnalytics(data.data);
                break;
            case 'new_user':
                this.showNewUserNotification(data.data);
                break;
            case 'game_activity':
                this.updateGameActivity(data.data);
                break;
        }
    }

    setupEventListeners() {
        // Mobile menu toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const sidebar = document.querySelector('.sidebar');
        
        if (mobileMenuBtn && sidebar) {
            mobileMenuBtn.addEventListener('click', () => {
                sidebar.classList.toggle('show');
            });
        }

        // Feature toggles
        document.querySelectorAll('.feature-toggle').forEach(toggle => {
            toggle.addEventListener('change', (e) => {
                this.updateFeature(e.target.dataset.feature, e.target.checked);
            });
        });

        // Wheel configuration
        const wheelForm = document.getElementById('wheel-config-form');
        if (wheelForm) {
            wheelForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveWheelConfiguration();
            });
        }

        // Shop item management
        const addItemBtn = document.getElementById('add-shop-item');
        if (addItemBtn) {
            addItemBtn.addEventListener('click', () => {
                this.showAddItemModal();
            });
        }

        // Chat command management
        const addCommandBtn = document.getElementById('add-chat-command');
        if (addCommandBtn) {
            addCommandBtn.addEventListener('click', () => {
                this.showAddCommandModal();
            });
        }
    }

    async loadDashboardData() {
        try {
            const response = await fetch('/api/streamers/dashboard/', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.updateDashboard(data);
            }
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    updateDashboard(data) {
        // Update stats cards
        this.updateStatsCards(data);
        
        // Update feature toggles
        this.updateFeatureToggles(data);
        
        // Update wheel configuration
        if (data.wheel_config) {
            this.updateWheelPreview(data.wheel_config);
        }
        
        // Update shop items
        if (data.shop_config) {
            this.updateShopItems(data.shop_config.items);
        }
        
        // Update chat commands
        if (data.chat_commands) {
            this.updateChatCommands(data.chat_commands);
        }
    }

    updateStatsCards(data) {
        const stats = {
            'total-viewers': data.total_viewers || 0,
            'total-games': data.total_games_played || 0,
            'points-distributed': data.total_points_distributed || 0,
            'subscription-status': data.subscription_active ? 'Active' : 'Inactive'
        };

        Object.entries(stats).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    updateFeatureToggles(data) {
        const features = {
            'wheel-enabled': data.wheel_enabled,
            'games-enabled': data.games_enabled,
            'shop-enabled': data.shop_enabled,
            'leaderboard-enabled': data.leaderboard_enabled,
            'obs-overlays-enabled': data.obs_overlays_enabled,
            'bot-enabled': data.bot_enabled
        };

        Object.entries(features).forEach(([id, enabled]) => {
            const toggle = document.getElementById(id);
            if (toggle) {
                toggle.checked = enabled;
            }
        });
    }

    async updateFeature(feature, enabled) {
        try {
            const response = await fetch('/api/streamers/profile/', {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                },
                body: JSON.stringify({
                    [`${feature}_enabled`]: enabled
                })
            });

            if (response.ok) {
                this.showNotification(`${feature} ${enabled ? 'enabled' : 'disabled'}`, 'success');
            } else {
                throw new Error('Failed to update feature');
            }
        } catch (error) {
            console.error('Error updating feature:', error);
            this.showNotification('Failed to update feature', 'error');
        }
    }

    updateWheelPreview(config) {
        const wheelPreview = document.getElementById('wheel-preview');
        if (!wheelPreview || !config.prizes) return;

        const prizes = config.prizes;
        const colors = config.wheel_colors || ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
        
        wheelPreview.innerHTML = '<div class="wheel-pointer"></div>';
        
        const segmentAngle = 360 / prizes.length;
        
        prizes.forEach((prize, index) => {
            const segment = document.createElement('div');
            segment.className = 'wheel-segment';
            segment.style.backgroundColor = colors[index % colors.length];
            segment.style.transform = `rotate(${index * segmentAngle}deg)`;
            segment.textContent = prize.name;
            wheelPreview.appendChild(segment);
        });
    }

    async saveWheelConfiguration() {
        const form = document.getElementById('wheel-config-form');
        const formData = new FormData(form);
        
        const config = {
            trigger_keyword: formData.get('trigger_keyword'),
            min_points_to_spin: parseInt(formData.get('min_points_to_spin')),
            max_points_per_spin: parseInt(formData.get('max_points_per_spin')),
            spin_cooldown: parseInt(formData.get('spin_cooldown')),
            auto_remove_idle: formData.get('auto_remove_idle') === 'on',
            idle_timeout: parseInt(formData.get('idle_timeout'))
        };

        try {
            const response = await fetch('/api/streamers/wheel/', {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                },
                body: JSON.stringify(config)
            });

            if (response.ok) {
                this.showNotification('Wheel configuration saved', 'success');
            } else {
                throw new Error('Failed to save configuration');
            }
        } catch (error) {
            console.error('Error saving wheel configuration:', error);
            this.showNotification('Failed to save configuration', 'error');
        }
    }

    setupCharts() {
        // Setup analytics charts using Chart.js
        this.setupAnalyticsChart();
        this.setupGameStatsChart();
    }

    setupAnalyticsChart() {
        const ctx = document.getElementById('analytics-chart');
        if (!ctx) return;

        this.charts.analytics = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Unique Visitors',
                    data: [],
                    borderColor: '#6366f1',
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    setupGameStatsChart() {
        const ctx = document.getElementById('game-stats-chart');
        if (!ctx) return;

        this.charts.gameStats = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Wheel Spins', 'Slot Games', 'Dice Games'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: ['#6366f1', '#8b5cf6', '#10b981']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    updateAnalytics(data) {
        // Update analytics chart
        if (this.charts.analytics) {
            this.charts.analytics.data.labels = data.dates;
            this.charts.analytics.data.datasets[0].data = data.visitors;
            this.charts.analytics.update();
        }
    }

    updateGameActivity(data) {
        // Update game stats chart
        if (this.charts.gameStats) {
            this.charts.gameStats.data.datasets[0].data = [
                data.wheel_games,
                data.slot_games,
                data.dice_games
            ];
            this.charts.gameStats.update();
        }
    }

    showNewUserNotification(data) {
        this.showNotification(`New user joined: ${data.username}`, 'info');
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} notification`;
        notification.textContent = message;
        
        const container = document.getElementById('notifications') || document.body;
        container.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    showAddItemModal() {
        // Show modal for adding shop items
        const modal = new bootstrap.Modal(document.getElementById('add-item-modal'));
        modal.show();
    }

    showAddCommandModal() {
        // Show modal for adding chat commands
        const modal = new bootstrap.Modal(document.getElementById('add-command-modal'));
        modal.show();
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.pathname.includes('/dashboard/')) {
        new CasinoXDashboard();
    }
});
