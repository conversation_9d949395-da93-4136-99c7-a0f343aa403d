#!/usr/bin/env python3
"""
Complete platform testing script
"""

import os
import sys
import django
import requests
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'casinox.settings')
django.setup()

def test_all_features():
    """Test all platform features"""
    print("🎰 Complete CasinoX Platform Test")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test all pages
    pages_to_test = [
        ('/', 'Landing Page'),
        ('/about/', 'Features Page'),
        ('/pricing/', 'Pricing Page'),
        ('/admin/', 'Admin Panel'),
        ('/s/teststreamer/', 'Test Streamer Page'),
        ('/api/streamers/plans/', 'API - Subscription Plans'),
        ('/s/teststreamer/api/', 'API - Streamer Data'),
    ]
    
    print("🌐 Testing All Pages:")
    for url, name in pages_to_test:
        try:
            response = requests.get(f"{base_url}{url}", timeout=5)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"   {status} {name}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")
    
    # Test database
    print(f"\n🗄️ Testing Database:")
    from accounts.models import User
    from streamers.models import Streamer, SubscriptionPlan
    from games.models import GameSession
    
    print(f"   ✅ Users: {User.objects.count()}")
    print(f"   ✅ Streamers: {Streamer.objects.count()}")
    print(f"   ✅ Plans: {SubscriptionPlan.objects.count()}")
    print(f"   ✅ Game Sessions: {GameSession.objects.count()}")
    
    # Test features
    print(f"\n🎮 Testing Features:")
    try:
        test_streamer = Streamer.objects.get(user__username='teststreamer')
        print(f"   ✅ Wheel Enabled: {test_streamer.wheel_enabled}")
        print(f"   ✅ Games Enabled: {test_streamer.games_enabled}")
        print(f"   ✅ Shop Enabled: {test_streamer.shop_enabled}")
        print(f"   ✅ Bot Enabled: {test_streamer.bot_enabled}")
        
        # Check configurations
        if hasattr(test_streamer, 'wheel_config'):
            print(f"   ✅ Wheel Config: {len(test_streamer.wheel_config.prizes)} prizes")
        if hasattr(test_streamer, 'shop_config'):
            print(f"   ✅ Shop Config: {test_streamer.shop_config.shopitem_set.count()} items")
            
    except Exception as e:
        print(f"   ❌ Feature test error: {e}")


def print_testing_checklist():
    """Print manual testing checklist"""
    print(f"\n✅ Manual Testing Checklist:")
    print("=" * 50)
    
    checklist = [
        "🏠 Visit landing page: http://localhost:8000/",
        "📋 Check features page: http://localhost:8000/about/",
        "💰 Check pricing page: http://localhost:8000/pricing/",
        "🔐 Login to admin: http://localhost:8000/admin/ (admin/admin123)",
        "🎮 Visit streamer page: http://localhost:8000/s/teststreamer/",
        "🔑 Test Discord login (after OAuth setup)",
        "🎲 Test wheel spin functionality",
        "🎰 Test mini games (slots, dice)",
        "🛒 Test points shop",
        "📊 Check leaderboard",
        "⚙️ Test streamer dashboard features",
        "📱 Test mobile responsiveness",
        "🔄 Test real-time updates",
        "💳 Test payment flow (with test keys)",
        "📈 Check analytics dashboard"
    ]
    
    for i, item in enumerate(checklist, 1):
        print(f"{i:2d}. {item}")


def print_production_deployment():
    """Print production deployment steps"""
    print(f"\n🚀 Production Deployment Steps:")
    print("=" * 50)
    
    steps = [
        "1. 🌐 Get a domain name",
        "2. 🖥️ Set up a VPS/server (DigitalOcean, AWS, etc.)",
        "3. 🐳 Install Docker and Docker Compose",
        "4. 📝 Update .env.production with real values",
        "5. 🔒 Get SSL certificates (Let's Encrypt)",
        "6. 🐘 Set up PostgreSQL database",
        "7. 🔴 Set up Redis server",
        "8. 💳 Configure payment providers",
        "9. 📧 Set up email service",
        "10. 🚀 Deploy: ./deploy.sh",
        "11. 🔍 Set up monitoring and logging",
        "12. 📦 Set up automated backups",
        "13. 🛡️ Configure security and firewall",
        "14. 📊 Set up analytics tracking",
        "15. 🎉 Launch and start onboarding streamers!"
    ]
    
    for step in steps:
        print(f"    {step}")


def print_monetization_strategy():
    """Print monetization and scaling strategy"""
    print(f"\n💰 Monetization & Scaling Strategy:")
    print("=" * 50)
    
    print("📈 Revenue Streams:")
    print("   • Monthly subscriptions ($10-$50/month per streamer)")
    print("   • Transaction fees on points purchases")
    print("   • Premium features and add-ons")
    print("   • White-label licensing")
    print("   • Custom development services")
    
    print(f"\n🎯 Target Market:")
    print("   • Kick streamers (primary)")
    print("   • Twitch streamers (expansion)")
    print("   • YouTube streamers (future)")
    print("   • Gaming communities")
    print("   • Casino/gambling content creators")
    
    print(f"\n📊 Growth Metrics to Track:")
    print("   • Monthly Recurring Revenue (MRR)")
    print("   • Customer Acquisition Cost (CAC)")
    print("   • Lifetime Value (LTV)")
    print("   • Churn rate")
    print("   • Active streamers")
    print("   • Total viewers served")


def main():
    """Run complete platform test"""
    test_all_features()
    print_testing_checklist()
    print_production_deployment()
    print_monetization_strategy()
    
    print(f"\n" + "=" * 50)
    print("🎉 CasinoX Platform Status: READY FOR LAUNCH!")
    print("Next: Complete Discord OAuth setup and start testing")


if __name__ == '__main__':
    main()
