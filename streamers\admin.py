from django.contrib import admin
from .models import (
    SubscriptionPlan, Streamer, WheelConfiguration,
    ShopConfiguration, ShopItem, ShopPurchase,
    ChatCommand, OBSOverlay, VerificationCode, UserSession
)


@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin):
    """Admin interface for subscription plans"""
    list_display = ['display_name', 'name', 'price_monthly', 'max_concurrent_users', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['display_name', 'name']


@admin.register(Streamer)
class StreamerAdmin(admin.ModelAdmin):
    """Admin interface for streamers"""
    list_display = [
        'display_name', 'kick_channel', 'subscription_plan',
        'subscription_active', 'is_active', 'created_at'
    ]
    list_filter = [
        'subscription_plan', 'subscription_active', 'is_active',
        'wheel_enabled', 'games_enabled', 'shop_enabled'
    ]
    search_fields = ['display_name', 'kick_channel', 'user__username']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(WheelConfiguration)
class WheelConfigurationAdmin(admin.ModelAdmin):
    """Admin interface for wheel configurations"""
    list_display = [
        'streamer', 'trigger_keyword', 'min_points_to_spin',
        'max_points_per_spin', 'spin_cooldown'
    ]
    search_fields = ['streamer__display_name', 'trigger_keyword']


@admin.register(ShopConfiguration)
class ShopConfigurationAdmin(admin.ModelAdmin):
    """Admin interface for shop configurations"""
    list_display = ['streamer', 'is_enabled', 'created_at']
    list_filter = ['is_enabled']
    search_fields = ['streamer__display_name']


@admin.register(ShopItem)
class ShopItemAdmin(admin.ModelAdmin):
    """Admin interface for shop items"""
    list_display = [
        'name', 'shop', 'price', 'is_available',
        'stock_quantity', 'category'
    ]
    list_filter = ['is_available', 'category', 'shop__streamer']
    search_fields = ['name', 'description']


@admin.register(ShopPurchase)
class ShopPurchaseAdmin(admin.ModelAdmin):
    """Admin interface for shop purchases"""
    list_display = [
        'purchase_id', 'user', 'item', 'quantity',
        'total_price', 'status', 'created_at'
    ]
    list_filter = ['status', 'created_at']
    search_fields = ['user__username', 'item__name', 'purchase_id']
    readonly_fields = ['purchase_id', 'created_at']


@admin.register(ChatCommand)
class ChatCommandAdmin(admin.ModelAdmin):
    """Admin interface for chat commands"""
    list_display = [
        'streamer', 'command', 'is_enabled', 'cooldown',
        'usage_count', 'last_used'
    ]
    list_filter = ['is_enabled', 'mod_only', 'subscriber_only']
    search_fields = ['streamer__display_name', 'command']


@admin.register(OBSOverlay)
class OBSOverlayAdmin(admin.ModelAdmin):
    """Admin interface for OBS overlays"""
    list_display = [
        'streamer', 'name', 'overlay_type', 'is_enabled',
        'width', 'height', 'created_at'
    ]
    list_filter = ['overlay_type', 'is_enabled']
    search_fields = ['streamer__display_name', 'name']
    readonly_fields = ['overlay_id']


@admin.register(VerificationCode)
class VerificationCodeAdmin(admin.ModelAdmin):
    """Admin interface for VerificationCode model"""
    list_display = [
        'code', 'user', 'streamer', 'is_used', 'expires_at',
        'created_at', 'kick_username'
    ]
    list_filter = ['is_used', 'created_at', 'expires_at']
    search_fields = ['code', 'user__username', 'kick_username']
    readonly_fields = ['created_at', 'used_at']
    ordering = ['-created_at']


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    """Admin interface for UserSession model"""
    list_display = [
        'user', 'session_id', 'ip_address', 'started_at', 'ended_at', 'streamer'
    ]
    list_filter = ['started_at', 'ended_at']
    search_fields = ['user__username', 'ip_address']
    readonly_fields = ['session_id', 'started_at']
    ordering = ['-started_at']
