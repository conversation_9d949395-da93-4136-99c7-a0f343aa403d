from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import login
from django.conf import settings
from django.utils import timezone
import requests
import logging

from .models import User, VerificationCode, PointsTransaction
from .serializers import (
    UserSerializer, LoginSerializer, DiscordAuthSerializer,
    VerificationCodeSerializer, GenerateCodeSerializer,
    VerifyCodeSerializer, PointsTransactionSerializer,
    DailyRewardSerializer, UserStatsSerializer
)
from streamers.models import Streamer

logger = logging.getLogger(__name__)


class UserProfileView(generics.RetrieveUpdateAPIView):
    """Get and update user profile"""
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def login_view(request):
    """Login endpoint for streamers and admins"""
    serializer = LoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']
        
        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token
        
        return Response({
            'access_token': str(access_token),
            'refresh_token': str(refresh),
            'user': UserSerializer(user).data
        })
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def discord_auth(request):
    """Discord OAuth authentication for viewers"""
    serializer = DiscordAuthSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    code = serializer.validated_data['code']
    
    # Exchange code for access token
    token_data = {
        'client_id': settings.DISCORD_CLIENT_ID,
        'client_secret': settings.DISCORD_CLIENT_SECRET,
        'grant_type': 'authorization_code',
        'code': code,
        'redirect_uri': settings.DISCORD_REDIRECT_URI,
    }
    
    try:
        # Get access token from Discord
        token_response = requests.post(
            'https://discord.com/api/oauth2/token',
            data=token_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        token_response.raise_for_status()
        token_json = token_response.json()
        
        # Get user info from Discord
        headers = {'Authorization': f"Bearer {token_json['access_token']}"}
        user_response = requests.get('https://discord.com/api/users/@me', headers=headers)
        user_response.raise_for_status()
        discord_user = user_response.json()
        
        # Create or get user
        user, created = User.objects.get_or_create(
            discord_id=discord_user['id'],
            defaults={
                'username': discord_user['username'],
                'discord_username': f"{discord_user['username']}#{discord_user['discriminator']}",
                'discord_avatar': f"https://cdn.discordapp.com/avatars/{discord_user['id']}/{discord_user['avatar']}.png" if discord_user['avatar'] else None,
                'user_type': 'viewer'
            }
        )
        
        # Update user info if not created
        if not created:
            user.discord_username = f"{discord_user['username']}#{discord_user['discriminator']}"
            user.discord_avatar = f"https://cdn.discordapp.com/avatars/{discord_user['id']}/{discord_user['avatar']}.png" if discord_user['avatar'] else None
            user.save()
        
        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token
        
        return Response({
            'access_token': str(access_token),
            'refresh_token': str(refresh),
            'user': UserSerializer(user).data,
            'is_new_user': created
        })
        
    except requests.RequestException as e:
        logger.error(f"Discord OAuth error: {e}")
        return Response(
            {'error': 'Failed to authenticate with Discord'},
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def generate_verification_code(request):
    """Generate verification code for linking Discord to Kick"""
    serializer = GenerateCodeSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    streamer_username = serializer.validated_data['streamer_username']
    
    try:
        streamer = Streamer.objects.get(user__username=streamer_username)
    except Streamer.DoesNotExist:
        return Response(
            {'error': 'Streamer not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    
    # Check if user already has an active code for this streamer
    existing_code = VerificationCode.objects.filter(
        user=request.user,
        streamer=streamer,
        is_used=False,
        expires_at__gt=timezone.now()
    ).first()
    
    if existing_code:
        return Response({
            'code': existing_code.code,
            'expires_at': existing_code.expires_at,
            'message': 'You already have an active verification code'
        })
    
    # Generate new code
    ip_address = request.META.get('REMOTE_ADDR')
    verification_code = VerificationCode.generate_code(
        user=request.user,
        streamer=streamer,
        ip_address=ip_address
    )
    
    return Response({
        'code': verification_code.code,
        'expires_at': verification_code.expires_at,
        'message': f'Paste this code in {streamer.user.username}\'s chat: !verify {verification_code.code}'
    })


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def verify_code_in_chat(request):
    """Verify code when used in Kick chat (called by chat bot)"""
    serializer = VerifyCodeSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    code = serializer.validated_data['code']
    kick_username = serializer.validated_data['kick_username']
    streamer_username = serializer.validated_data['streamer_username']
    
    try:
        verification_code = VerificationCode.objects.get(
            code=code,
            streamer__user__username=streamer_username,
            is_used=False
        )
        
        if verification_code.is_expired():
            return Response({
                'success': False,
                'message': 'Verification code has expired'
            })
        
        # Use the code
        if verification_code.use_code(kick_username):
            # Give welcome bonus points
            verification_code.user.add_points(100, "Welcome bonus for verification")
            
            return Response({
                'success': True,
                'message': f'Successfully verified! Welcome {kick_username}, you received 100 bonus points!',
                'user_id': verification_code.user.id,
                'discord_username': verification_code.user.discord_username
            })
        else:
            return Response({
                'success': False,
                'message': 'Failed to verify code'
            })
            
    except VerificationCode.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Invalid verification code'
        })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def claim_daily_reward(request):
    """Claim daily reward points"""
    user = request.user
    
    if not user.can_claim_daily_reward():
        return Response(
            {'error': 'Daily reward already claimed today'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Give daily reward (50 points)
    reward_amount = 50
    user.add_points(reward_amount, "Daily reward")
    user.last_daily_reward = timezone.now()
    user.save()
    
    return Response({
        'message': f'Daily reward claimed! You received {reward_amount} points.',
        'points_earned': reward_amount,
        'total_points': user.points
    })


class PointsTransactionListView(generics.ListAPIView):
    """List user's points transactions"""
    serializer_class = PointsTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return PointsTransaction.objects.filter(user=self.request.user)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_stats(request):
    """Get user statistics"""
    serializer = UserStatsSerializer(request.user)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def leaderboard(request):
    """Get global leaderboard"""
    streamer_username = request.GET.get('streamer')
    
    if streamer_username:
        # Get leaderboard for specific streamer
        # This would require tracking per-streamer stats
        users = User.objects.filter(
            user_type='viewer',
            is_verified=True
        ).order_by('-points')[:50]
    else:
        # Global leaderboard
        users = User.objects.filter(
            user_type='viewer',
            is_verified=True
        ).order_by('-points')[:50]
    
    serializer = UserStatsSerializer(users, many=True)
    return Response(serializer.data)
