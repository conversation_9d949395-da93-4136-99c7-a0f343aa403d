from django.urls import path
from . import views

urlpatterns = [
    # Chat bot management
    path('connections/', views.ChatConnectionListView.as_view(), name='chat_connections'),
    path('connections/<int:pk>/', views.ChatConnectionDetailView.as_view(), name='chat_connection_detail'),
    
    # Chat messages and analytics
    path('messages/', views.ChatMessageListView.as_view(), name='chat_messages'),
    path('analytics/', views.chat_analytics, name='chat_analytics'),
    
    # Manual chat commands
    path('send-message/', views.send_chat_message, name='send_chat_message'),
    
    # Kick chat webhook (for receiving messages)
    path('webhook/kick/', views.kick_chat_webhook, name='kick_chat_webhook'),
]
