from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()


class KickChatConnection(models.Model):
    """Manage Kick chat connections for streamers"""
    streamer = models.OneToOneField('streamers.Streamer', on_delete=models.CASCADE)
    
    # Connection details
    kick_channel_id = models.CharField(max_length=100)
    is_connected = models.BooleanField(default=False)
    connection_token = models.CharField(max_length=500, blank=True)
    
    # Bot settings
    bot_username = models.CharField(max_length=100, default='CasinoXBot')
    is_active = models.BooleanField(default=True)
    
    # Connection stats
    last_connected = models.DateTimeField(null=True, blank=True)
    last_disconnected = models.DateTimeField(null=True, blank=True)
    total_messages_processed = models.IntegerField(default=0)
    
    # Error tracking
    last_error = models.TextField(blank=True)
    error_count = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Chat connection for {self.streamer.display_name}"

    def mark_connected(self):
        """Mark connection as active"""
        self.is_connected = True
        self.last_connected = timezone.now()
        self.save()

    def mark_disconnected(self, error=None):
        """Mark connection as inactive"""
        self.is_connected = False
        self.last_disconnected = timezone.now()
        if error:
            self.last_error = str(error)
            self.error_count += 1
        self.save()


class ChatMessage(models.Model):
    """Store chat messages for processing and analytics"""
    MESSAGE_TYPES = (
        ('message', 'Regular Message'),
        ('command', 'Bot Command'),
        ('verification', 'Verification Code'),
        ('system', 'System Message'),
    )
    
    connection = models.ForeignKey(KickChatConnection, on_delete=models.CASCADE)
    
    # Message details
    kick_message_id = models.CharField(max_length=100, unique=True)
    username = models.CharField(max_length=100)
    user_id = models.CharField(max_length=100, null=True, blank=True)
    content = models.TextField()
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, default='message')
    
    # Processing
    is_processed = models.BooleanField(default=False)
    processed_at = models.DateTimeField(null=True, blank=True)
    bot_response = models.TextField(blank=True)
    
    # Metadata
    timestamp = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.username}: {self.content[:50]}"

    def mark_processed(self, response=None):
        """Mark message as processed"""
        self.is_processed = True
        self.processed_at = timezone.now()
        if response:
            self.bot_response = response
        self.save()


class CommandCooldown(models.Model):
    """Track command cooldowns per user"""
    connection = models.ForeignKey(KickChatConnection, on_delete=models.CASCADE)
    username = models.CharField(max_length=100)
    command = models.CharField(max_length=50)
    last_used = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['connection', 'username', 'command']

    def __str__(self):
        return f"{self.username} - {self.command} cooldown"

    @classmethod
    def is_on_cooldown(cls, connection, username, command, cooldown_seconds):
        """Check if user is on cooldown for a command"""
        try:
            cooldown_record = cls.objects.get(
                connection=connection,
                username=username,
                command=command
            )
            time_diff = timezone.now() - cooldown_record.last_used
            return time_diff.total_seconds() < cooldown_seconds
        except cls.DoesNotExist:
            return False

    @classmethod
    def set_cooldown(cls, connection, username, command):
        """Set cooldown for user command"""
        cooldown_record, created = cls.objects.get_or_create(
            connection=connection,
            username=username,
            command=command
        )
        if not created:
            cooldown_record.last_used = timezone.now()
            cooldown_record.save()


class BotResponse(models.Model):
    """Track bot responses and their effectiveness"""
    connection = models.ForeignKey(KickChatConnection, on_delete=models.CASCADE)
    original_message = models.ForeignKey(ChatMessage, on_delete=models.CASCADE)
    
    # Response details
    response_text = models.TextField()
    response_type = models.CharField(max_length=50)  # 'command', 'verification', 'auto_reply'
    
    # Success tracking
    was_sent = models.BooleanField(default=False)
    send_error = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Bot response to {self.original_message.username}"


class ChatAnalytics(models.Model):
    """Analytics for chat activity"""
    connection = models.ForeignKey(KickChatConnection, on_delete=models.CASCADE)
    date = models.DateField()
    
    # Message stats
    total_messages = models.IntegerField(default=0)
    unique_users = models.IntegerField(default=0)
    commands_used = models.IntegerField(default=0)
    verifications_attempted = models.IntegerField(default=0)
    verifications_successful = models.IntegerField(default=0)
    
    # Bot stats
    bot_responses = models.IntegerField(default=0)
    bot_errors = models.IntegerField(default=0)
    
    # Popular commands (JSON)
    command_usage = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['connection', 'date']

    def __str__(self):
        return f"Analytics for {self.connection.streamer.display_name} - {self.date}"


class WebSocketConnection(models.Model):
    """Track WebSocket connections for real-time updates"""
    CONNECTION_TYPES = (
        ('streamer_dashboard', 'Streamer Dashboard'),
        ('viewer_page', 'Viewer Page'),
        ('obs_overlay', 'OBS Overlay'),
    )
    
    connection_id = models.UUIDField(default=uuid.uuid4, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    streamer = models.ForeignKey('streamers.Streamer', on_delete=models.CASCADE)
    connection_type = models.CharField(max_length=30, choices=CONNECTION_TYPES)
    
    # Connection details
    is_active = models.BooleanField(default=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    
    # Timestamps
    connected_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    disconnected_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"WebSocket {self.connection_type} for {self.streamer.display_name}"

    def disconnect(self):
        """Mark connection as disconnected"""
        self.is_active = False
        self.disconnected_at = timezone.now()
        self.save()
