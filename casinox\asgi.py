"""
ASGI config for CasinoX project.
"""

import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
import chat_bot.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'casinox.settings')

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(
            chat_bot.routing.websocket_urlpatterns
        )
    ),
})
