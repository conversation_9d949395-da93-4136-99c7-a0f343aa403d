from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, VerificationCode, PointsTransaction, UserSession


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Admin interface for User model"""
    list_display = [
        'username', 'email', 'user_type', 'discord_username', 
        'kick_username', 'points', 'is_verified', 'created_at'
    ]
    list_filter = ['user_type', 'is_verified', 'created_at']
    search_fields = ['username', 'email', 'discord_username', 'kick_username']
    ordering = ['-created_at']
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('CasinoX Profile', {
            'fields': (
                'user_type', 'discord_id', 'discord_username', 'discord_avatar',
                'kick_username', 'points', 'total_wins', 'total_games_played',
                'is_verified', 'last_daily_reward'
            )
        }),
    )


@admin.register(VerificationCode)
class VerificationCodeAdmin(admin.ModelAdmin):
    """Admin interface for VerificationCode model"""
    list_display = [
        'code', 'user', 'streamer', 'is_used', 'expires_at', 
        'created_at', 'kick_username'
    ]
    list_filter = ['is_used', 'created_at', 'expires_at']
    search_fields = ['code', 'user__username', 'kick_username']
    readonly_fields = ['created_at', 'used_at']
    ordering = ['-created_at']


@admin.register(PointsTransaction)
class PointsTransactionAdmin(admin.ModelAdmin):
    """Admin interface for PointsTransaction model"""
    list_display = [
        'user', 'amount', 'transaction_type', 'reason', 'created_at'
    ]
    list_filter = ['transaction_type', 'created_at']
    search_fields = ['user__username', 'reason']
    readonly_fields = ['created_at']
    ordering = ['-created_at']


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    """Admin interface for UserSession model"""
    list_display = [
        'user', 'session_id', 'ip_address', 'started_at', 'ended_at', 'streamer'
    ]
    list_filter = ['started_at', 'ended_at']
    search_fields = ['user__username', 'ip_address']
    readonly_fields = ['session_id', 'started_at']
    ordering = ['-started_at']
