# Generated by Django 4.2.7 on 2025-07-18 20:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("streamers", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="GameSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_id", models.UUIDField(default=uuid.uuid4, unique=True)),
                (
                    "game_type",
                    models.CharField(
                        choices=[
                            ("wheel", "Wheel of Fortune"),
                            ("slot", "Slot Machine"),
                            ("dice", "Dice Roll"),
                            ("double", "Double or Nothing"),
                            ("crash", "Crash Game"),
                        ],
                        max_length=20,
                    ),
                ),
                ("bet_amount", models.IntegerField()),
                ("potential_win", models.IntegerField(blank=True, null=True)),
                ("actual_win", models.IntegerField(default=0)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("game_data", models.JSONField(default=dict)),
                ("result_data", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                (
                    "streamer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="streamers.streamer",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="WheelSpin",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("prizes", models.JSONField()),
                ("selected_prize_index", models.IntegerField()),
                ("selected_prize", models.JSONField()),
                ("spin_duration", models.FloatField(default=3.0)),
                ("final_rotation", models.FloatField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "session",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="games.gamesession",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="SlotMachine",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("reels", models.JSONField()),
                ("paylines", models.JSONField()),
                ("multiplier", models.FloatField(default=1.0)),
                ("symbols", models.JSONField(default=list)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "session",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="games.gamesession",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="GameStatistics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_games", models.IntegerField(default=0)),
                ("total_bets", models.BigIntegerField(default=0)),
                ("total_wins", models.BigIntegerField(default=0)),
                ("total_players", models.IntegerField(default=0)),
                ("wheel_games", models.IntegerField(default=0)),
                ("slot_games", models.IntegerField(default=0)),
                ("dice_games", models.IntegerField(default=0)),
                ("today_games", models.IntegerField(default=0)),
                ("today_bets", models.BigIntegerField(default=0)),
                ("today_wins", models.BigIntegerField(default=0)),
                ("last_reset", models.DateField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "streamer",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="streamers.streamer",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DiceRoll",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("num_dice", models.IntegerField(default=2)),
                ("dice_results", models.JSONField()),
                ("total_roll", models.IntegerField()),
                ("bet_type", models.CharField(max_length=20)),
                ("bet_value", models.IntegerField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "session",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="games.gamesession",
                    ),
                ),
            ],
        ),
    ]
