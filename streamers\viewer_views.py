from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from .models import Streamer
from .serializers import StreamerPublicSerializer


def streamer_page(request, streamer_username):
    """Public streamer page for viewers"""
    streamer = get_object_or_404(
        Streamer, 
        user__username=streamer_username,
        is_active=True
    )
    
    context = {
        'streamer': streamer,
        'streamer_data': StreamerPublicSerializer(streamer).data
    }
    
    return render(request, 'viewer/streamer_page.html', context)


@api_view(['GET'])
@permission_classes([AllowAny])
def streamer_public_api(request, streamer_username):
    """API endpoint for public streamer data"""
    try:
        streamer = Streamer.objects.get(
            user__username=streamer_username,
            is_active=True
        )
        serializer = StreamerPublicSerializer(streamer)
        return JsonResponse(serializer.data)
    except Streamer.DoesNotExist:
        return JsonResponse(
            {'error': 'Streamer not found'},
            status=404
        )
