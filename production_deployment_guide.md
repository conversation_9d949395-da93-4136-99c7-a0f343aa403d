# CasinoX Production Deployment Guide

## Step 1: Domain & Hosting (15 minutes)

### Get a Domain:
- **Recommended**: Namecheap, GoDaddy, or Cloudflare
- **Suggestions**: 
  - casinox.io
  - streamcasino.com
  - kickcasino.app
  - casinoxsaas.com
- **Cost**: $10-15/year

### Get a VPS Server:
- **Recommended**: DigitalOcean, Linode, or Vultr
- **Specs**: 2GB RAM, 1 CPU, 50GB SSD ($12/month)
- **OS**: Ubuntu 22.04 LTS

## Step 2: Server Setup (30 minutes)

### Connect to your server:
```bash
ssh root@your-server-ip
```

### Install Docker:
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
apt install docker-compose-plugin
```

### Install Git:
```bash
apt update
apt install git
```

## Step 3: Deploy CasinoX (20 minutes)

### Clone your project:
```bash
git clone https://github.com/yourusername/casinox.git
cd casinox
```

### Update production environment:
```bash
cp .env.production .env
nano .env
```

### Update these values in .env:
```
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
SECRET_KEY=your-super-secret-production-key-here

# Database
DB_NAME=casinox_prod
DB_USER=casinox_user  
DB_PASSWORD=your-secure-db-password

# Discord OAuth (update with production domain)
DISCORD_REDIRECT_URI=https://yourdomain.com/auth/discord/callback/

# Email
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
```

### Deploy:
```bash
chmod +x deploy.sh
./deploy.sh
```

## Step 4: SSL Certificate (10 minutes)

### Install Certbot:
```bash
apt install certbot python3-certbot-nginx
```

### Get SSL certificate:
```bash
certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

## Step 5: DNS Configuration (5 minutes)

### Point your domain to server:
- **A Record**: yourdomain.com → your-server-ip
- **A Record**: www.yourdomain.com → your-server-ip

## Step 6: Final Testing (10 minutes)

### Test your production site:
- Visit: https://yourdomain.com
- Test Discord OAuth
- Test all features
- Check admin panel

## Total Time: ~90 minutes
## Total Cost: ~$25/month ($12 server + $1 domain + $12 extras)

## Post-Deployment Checklist:
- [ ] Domain pointing to server
- [ ] SSL certificate installed
- [ ] Discord OAuth working
- [ ] Database migrations complete
- [ ] Admin user created
- [ ] All pages loading
- [ ] Payment providers configured
- [ ] Email sending working
- [ ] Backups configured
