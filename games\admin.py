from django.contrib import admin
from .models import GameSession, WheelSpin, SlotMachine, DiceRoll, GameStatistics


@admin.register(GameSession)
class GameSessionAdmin(admin.ModelAdmin):
    """Admin interface for game sessions"""
    list_display = [
        'session_id', 'user', 'streamer', 'game_type', 'bet_amount',
        'actual_win', 'status', 'created_at', 'completed_at'
    ]
    list_filter = ['game_type', 'status', 'created_at', 'streamer']
    search_fields = ['user__username', 'streamer__display_name', 'session_id']
    readonly_fields = ['session_id', 'created_at', 'started_at', 'completed_at']
    ordering = ['-created_at']

    def get_queryset(self, request):
        # Limit to recent sessions for performance
        from django.utils import timezone
        from datetime import timedelta
        
        qs = super().get_queryset(request)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        return qs.filter(created_at__gte=thirty_days_ago)


@admin.register(WheelSpin)
class WheelSpinAdmin(admin.ModelAdmin):
    """Admin interface for wheel spins"""
    list_display = [
        'get_user', 'get_streamer', 'get_bet_amount', 'get_win_amount',
        'selected_prize_index', 'created_at'
    ]
    list_filter = ['created_at', 'session__streamer']
    search_fields = ['session__user__username', 'session__streamer__display_name']
    readonly_fields = ['created_at']

    def get_user(self, obj):
        return obj.session.user.username
    get_user.short_description = 'User'

    def get_streamer(self, obj):
        return obj.session.streamer.display_name
    get_streamer.short_description = 'Streamer'

    def get_bet_amount(self, obj):
        return obj.session.bet_amount
    get_bet_amount.short_description = 'Bet Amount'

    def get_win_amount(self, obj):
        return obj.session.actual_win
    get_win_amount.short_description = 'Win Amount'


@admin.register(SlotMachine)
class SlotMachineAdmin(admin.ModelAdmin):
    """Admin interface for slot machine games"""
    list_display = [
        'get_user', 'get_streamer', 'get_bet_amount', 'get_win_amount',
        'multiplier', 'created_at'
    ]
    list_filter = ['created_at', 'session__streamer']
    search_fields = ['session__user__username', 'session__streamer__display_name']
    readonly_fields = ['created_at']

    def get_user(self, obj):
        return obj.session.user.username
    get_user.short_description = 'User'

    def get_streamer(self, obj):
        return obj.session.streamer.display_name
    get_streamer.short_description = 'Streamer'

    def get_bet_amount(self, obj):
        return obj.session.bet_amount
    get_bet_amount.short_description = 'Bet Amount'

    def get_win_amount(self, obj):
        return obj.session.actual_win
    get_win_amount.short_description = 'Win Amount'


@admin.register(DiceRoll)
class DiceRollAdmin(admin.ModelAdmin):
    """Admin interface for dice roll games"""
    list_display = [
        'get_user', 'get_streamer', 'get_bet_amount', 'get_win_amount',
        'total_roll', 'bet_type', 'bet_value', 'created_at'
    ]
    list_filter = ['bet_type', 'created_at', 'session__streamer']
    search_fields = ['session__user__username', 'session__streamer__display_name']
    readonly_fields = ['created_at']

    def get_user(self, obj):
        return obj.session.user.username
    get_user.short_description = 'User'

    def get_streamer(self, obj):
        return obj.session.streamer.display_name
    get_streamer.short_description = 'Streamer'

    def get_bet_amount(self, obj):
        return obj.session.bet_amount
    get_bet_amount.short_description = 'Bet Amount'

    def get_win_amount(self, obj):
        return obj.session.actual_win
    get_win_amount.short_description = 'Win Amount'


@admin.register(GameStatistics)
class GameStatisticsAdmin(admin.ModelAdmin):
    """Admin interface for game statistics"""
    list_display = [
        'streamer', 'total_games', 'total_bets', 'total_wins',
        'today_games', 'last_reset', 'updated_at'
    ]
    list_filter = ['last_reset', 'updated_at']
    search_fields = ['streamer__display_name']
    readonly_fields = ['updated_at']
