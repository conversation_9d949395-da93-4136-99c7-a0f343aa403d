<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Dashboard - CasinoX{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/dashboard.css">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="dashboard-container">
        <!-- Mobile Menu Button -->
        <button class="mobile-menu-btn d-md-none">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="p-3 border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-dice text-primary"></i> CasinoX
                </h5>
                <small class="text-muted">Streamer Dashboard</small>
            </div>
            
            <ul class="nav nav-pills flex-column sidebar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard/">
                        <i class="fas fa-tachometer-alt"></i> Overview
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard/wheel/">
                        <i class="fas fa-dharmachakra"></i> Wheel of Fortune
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard/games/">
                        <i class="fas fa-gamepad"></i> Mini Games
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard/shop/">
                        <i class="fas fa-shopping-cart"></i> Points Shop
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard/chat/">
                        <i class="fas fa-comments"></i> Chat Bot
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard/overlays/">
                        <i class="fas fa-layer-group"></i> OBS Overlays
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard/analytics/">
                        <i class="fas fa-chart-bar"></i> Analytics
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard/settings/">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </li>
            </ul>
            
            <div class="p-3 border-top mt-auto">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="fw-bold">{{ user.username }}</div>
                        <small class="text-muted">{{ user.streamer.subscription_plan.display_name|default:"Free Plan" }}</small>
                    </div>
                    <a href="/logout/" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{% block page_title %}Dashboard{% endblock %}</h1>
                    <p class="text-muted mb-0">{% block page_description %}Manage your streaming platform{% endblock %}</p>
                </div>
                <div>
                    <a href="/s/{{ user.username }}/" target="_blank" class="btn btn-outline-primary">
                        <i class="fas fa-external-link-alt"></i> View Public Page
                    </a>
                </div>
            </div>

            <!-- Notifications -->
            <div id="notifications"></div>

            <!-- Content -->
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        window.streamerUsername = '{{ user.username }}';
    </script>
    <script src="/static/js/dashboard.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
