from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from streamers.models import Streamer, SubscriptionPlan

User = get_user_model()


class Command(BaseCommand):
    help = 'Create a streamer account for testing'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Username for the streamer')
        parser.add_argument('--email', type=str, help='Email for the streamer')
        parser.add_argument('--password', type=str, help='Password for the streamer')
        parser.add_argument('--kick-channel', type=str, help='Kick channel name')
        parser.add_argument('--display-name', type=str, help='Display name')

    def handle(self, *args, **options):
        username = options.get('username') or 'teststreamer'
        email = options.get('email') or '<EMAIL>'
        password = options.get('password') or 'streamer123'
        kick_channel = options.get('kick_channel') or username
        display_name = options.get('display_name') or f'{username.title()} Gaming'

        # Check if user already exists
        if User.objects.filter(username=username).exists():
            self.stdout.write(
                self.style.WARNING(f'User {username} already exists')
            )
            return

        # Create user
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            user_type='streamer'
        )

        # Create streamer profile
        streamer = Streamer.objects.create(
            user=user,
            kick_channel=kick_channel,
            display_name=display_name,
            bio=f"Welcome to {display_name}! Join us for exciting casino games and win amazing prizes!",
            wheel_enabled=True,
            games_enabled=True,
            shop_enabled=True,
            leaderboard_enabled=True,
            bot_enabled=True
        )

        # Assign Pro plan for testing
        try:
            pro_plan = SubscriptionPlan.objects.get(name='pro')
            streamer.subscription_plan = pro_plan
            streamer.subscription_active = True
            streamer.save()
        except SubscriptionPlan.DoesNotExist:
            pass

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created streamer: {username}')
        )
        self.stdout.write(f'  Username: {username}')
        self.stdout.write(f'  Password: {password}')
        self.stdout.write(f'  Kick Channel: {kick_channel}')
        self.stdout.write(f'  Public Page: http://localhost:8000/s/{username}/')
        self.stdout.write(f'  Dashboard: http://localhost:8000/admin/ (login with these credentials)')
