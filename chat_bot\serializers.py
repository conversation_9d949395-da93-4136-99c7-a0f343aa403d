from rest_framework import serializers
from .models import (
    KickChatConnection, ChatMessage, CommandCooldown, 
    BotResponse, ChatAnalytics, WebSocketConnection
)


class KickChatConnectionSerializer(serializers.ModelSerializer):
    """Serializer for Kick chat connections"""
    streamer_name = serializers.CharField(source='streamer.display_name', read_only=True)
    
    class Meta:
        model = KickChatConnection
        fields = [
            'id', 'streamer_name', 'kick_channel_id', 'is_connected',
            'bot_username', 'is_active', 'last_connected', 'last_disconnected',
            'total_messages_processed', 'last_error', 'error_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'streamer_name', 'is_connected', 'last_connected',
            'last_disconnected', 'total_messages_processed', 'last_error',
            'error_count', 'created_at', 'updated_at'
        ]


class ChatMessageSerializer(serializers.ModelSerializer):
    """Serializer for chat messages"""
    
    class Meta:
        model = ChatMessage
        fields = [
            'id', 'kick_message_id', 'username', 'user_id', 'content',
            'message_type', 'is_processed', 'processed_at', 'bot_response',
            'timestamp', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class BotResponseSerializer(serializers.ModelSerializer):
    """Serializer for bot responses"""
    original_message_content = serializers.CharField(source='original_message.content', read_only=True)
    username = serializers.CharField(source='original_message.username', read_only=True)
    
    class Meta:
        model = BotResponse
        fields = [
            'id', 'original_message_content', 'username', 'response_text',
            'response_type', 'was_sent', 'send_error', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class ChatAnalyticsSerializer(serializers.ModelSerializer):
    """Serializer for chat analytics"""
    
    class Meta:
        model = ChatAnalytics
        fields = [
            'date', 'total_messages', 'unique_users', 'commands_used',
            'verifications_attempted', 'verifications_successful',
            'bot_responses', 'bot_errors', 'command_usage'
        ]


class WebSocketConnectionSerializer(serializers.ModelSerializer):
    """Serializer for WebSocket connections"""
    user_username = serializers.CharField(source='user.username', read_only=True)
    streamer_name = serializers.CharField(source='streamer.display_name', read_only=True)
    
    class Meta:
        model = WebSocketConnection
        fields = [
            'connection_id', 'user_username', 'streamer_name', 'connection_type',
            'is_active', 'ip_address', 'connected_at', 'last_activity',
            'disconnected_at'
        ]
        read_only_fields = ['connection_id', 'connected_at', 'last_activity', 'disconnected_at']


class SendMessageSerializer(serializers.Serializer):
    """Serializer for sending manual chat messages"""
    message = serializers.CharField(max_length=500)


class ChatCommandStatsSerializer(serializers.Serializer):
    """Serializer for chat command statistics"""
    command = serializers.CharField()
    usage_count = serializers.IntegerField()
    last_used = serializers.DateTimeField()
