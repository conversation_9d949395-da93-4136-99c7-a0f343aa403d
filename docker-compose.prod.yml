version: '3.8'

services:
  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      POSTGRES_DB: casinox_prod
      POSTGRES_USER: casinox_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    restart: unless-stopped
    networks:
      - casinox_network

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    networks:
      - casinox_network

  web:
    build: .
    command: gunicorn --bind 0.0.0.0:8000 --workers 4 casinox.wsgi:application
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    env_file:
      - .env.production
    restart: unless-stopped
    networks:
      - casinox_network

  celery:
    build: .
    command: celery -A casinox worker -l info
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    env_file:
      - .env.production
    restart: unless-stopped
    networks:
      - casinox_network

  celery-beat:
    build: .
    command: celery -A casinox beat -l info
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    env_file:
      - .env.production
    restart: unless-stopped
    networks:
      - casinox_network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
    restart: unless-stopped
    networks:
      - casinox_network

volumes:
  postgres_data:
  static_volume:
  media_volume:

networks:
  casinox_network:
    driver: bridge
