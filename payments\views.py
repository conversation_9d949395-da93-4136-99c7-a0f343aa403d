from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.conf import settings
from django.utils import timezone
import stripe
import requests
import logging

from .models import Payment, CryptoPayment, StripePayment
from streamers.models import SubscriptionPlan, Streamer

logger = logging.getLogger(__name__)
stripe.api_key = settings.STRIPE_SECRET_KEY


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_payment(request):
    """Create a new payment for subscription"""
    try:
        plan_id = request.data.get('plan_id')
        payment_method = request.data.get('payment_method')  # 'crypto' or 'stripe'
        months = request.data.get('months', 1)
        
        # Get subscription plan
        try:
            plan = SubscriptionPlan.objects.get(id=plan_id, is_active=True)
        except SubscriptionPlan.DoesNotExist:
            return Response(
                {'error': 'Invalid subscription plan'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Calculate total amount
        total_amount = plan.price_monthly * months
        
        # Create payment record
        payment = Payment.objects.create(
            user=request.user,
            amount=total_amount,
            payment_method=payment_method,
            subscription_plan=plan,
            subscription_months=months
        )
        
        if payment_method == 'crypto':
            return create_crypto_payment(payment)
        elif payment_method == 'stripe':
            return create_stripe_payment(payment)
        else:
            return Response(
                {'error': 'Invalid payment method'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
    except Exception as e:
        logger.error(f"Error creating payment: {e}")
        return Response(
            {'error': 'Failed to create payment'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def create_crypto_payment(payment):
    """Create crypto payment using NOWPayments"""
    try:
        # NOWPayments API call
        nowpayments_data = {
            'price_amount': float(payment.amount),
            'price_currency': 'USD',
            'pay_currency': 'btc',  # Default to BTC, can be made configurable
            'order_id': str(payment.payment_id),
            'order_description': f'CasinoX Subscription - {payment.subscription_plan.display_name}',
            'success_url': f'{settings.FRONTEND_URL}/payment/success/',
            'cancel_url': f'{settings.FRONTEND_URL}/payment/cancel/',
        }
        
        headers = {
            'x-api-key': settings.NOWPAYMENTS_API_KEY,
            'Content-Type': 'application/json'
        }
        
        response = requests.post(
            'https://api.nowpayments.io/v1/payment',
            json=nowpayments_data,
            headers=headers
        )
        
        if response.status_code == 201:
            payment_data = response.json()
            
            # Create crypto payment record
            crypto_payment = CryptoPayment.objects.create(
                payment=payment,
                cryptocurrency=payment_data.get('pay_currency', 'btc').upper(),
                crypto_amount=payment_data.get('pay_amount', 0),
                wallet_address=payment_data.get('pay_address', ''),
                nowpayments_id=payment_data.get('payment_id', ''),
                payment_url=payment_data.get('payment_url', '')
            )
            
            payment.external_payment_id = payment_data.get('payment_id', '')
            payment.payment_data = payment_data
            payment.status = 'processing'
            payment.save()
            
            return Response({
                'payment_id': payment.payment_id,
                'payment_url': payment_data.get('payment_url'),
                'crypto_amount': payment_data.get('pay_amount'),
                'cryptocurrency': payment_data.get('pay_currency', 'btc').upper(),
                'wallet_address': payment_data.get('pay_address'),
                'status': 'processing'
            })
        else:
            logger.error(f"NOWPayments error: {response.text}")
            return Response(
                {'error': 'Failed to create crypto payment'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
    except Exception as e:
        logger.error(f"Error creating crypto payment: {e}")
        return Response(
            {'error': 'Failed to create crypto payment'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def create_stripe_payment(payment):
    """Create Stripe payment intent"""
    try:
        # Create Stripe payment intent
        intent = stripe.PaymentIntent.create(
            amount=int(payment.amount * 100),  # Convert to cents
            currency='usd',
            metadata={
                'payment_id': str(payment.payment_id),
                'user_id': payment.user.id,
                'plan_id': payment.subscription_plan.id,
                'months': payment.subscription_months
            }
        )
        
        # Create Stripe payment record
        stripe_payment = StripePayment.objects.create(
            payment=payment,
            stripe_payment_intent_id=intent.id
        )
        
        payment.external_payment_id = intent.id
        payment.status = 'processing'
        payment.save()
        
        return Response({
            'payment_id': payment.payment_id,
            'client_secret': intent.client_secret,
            'status': 'processing'
        })
        
    except Exception as e:
        logger.error(f"Error creating Stripe payment: {e}")
        return Response(
            {'error': 'Failed to create Stripe payment'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def crypto_payment_callback(request):
    """Handle NOWPayments callback"""
    try:
        # Verify the callback (implement signature verification)
        payment_data = request.data
        payment_id = payment_data.get('order_id')
        payment_status = payment_data.get('payment_status')
        
        try:
            payment = Payment.objects.get(payment_id=payment_id)
        except Payment.DoesNotExist:
            return Response({'error': 'Payment not found'}, status=404)
        
        if payment_status == 'finished':
            # Payment completed
            payment.status = 'completed'
            payment.completed_at = timezone.now()
            payment.save()
            
            # Activate subscription
            activate_subscription(payment)
            
        elif payment_status in ['failed', 'expired']:
            payment.status = 'failed'
            payment.save()
        
        return Response({'status': 'ok'})
        
    except Exception as e:
        logger.error(f"Error handling crypto callback: {e}")
        return Response({'error': 'Callback processing failed'}, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def stripe_webhook(request):
    """Handle Stripe webhook"""
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    
    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
        )
    except ValueError:
        return Response({'error': 'Invalid payload'}, status=400)
    except stripe.error.SignatureVerificationError:
        return Response({'error': 'Invalid signature'}, status=400)
    
    if event['type'] == 'payment_intent.succeeded':
        payment_intent = event['data']['object']
        payment_id = payment_intent['metadata'].get('payment_id')
        
        try:
            payment = Payment.objects.get(payment_id=payment_id)
            payment.status = 'completed'
            payment.completed_at = timezone.now()
            payment.save()
            
            # Activate subscription
            activate_subscription(payment)
            
        except Payment.DoesNotExist:
            logger.error(f"Payment not found: {payment_id}")
    
    return Response({'status': 'ok'})


def activate_subscription(payment):
    """Activate subscription for user"""
    try:
        # Get or create streamer profile
        streamer, created = Streamer.objects.get_or_create(
            user=payment.user,
            defaults={
                'kick_channel': payment.user.username,
                'display_name': payment.user.username
            }
        )
        
        # Update subscription
        streamer.subscription_plan = payment.subscription_plan
        streamer.subscription_active = True
        
        # Calculate expiration date
        if streamer.subscription_expires_at and streamer.subscription_expires_at > timezone.now():
            # Extend existing subscription
            expiration = streamer.subscription_expires_at
        else:
            # New subscription
            expiration = timezone.now()
        
        # Add months to expiration
        from dateutil.relativedelta import relativedelta
        streamer.subscription_expires_at = expiration + relativedelta(months=payment.subscription_months)
        streamer.save()
        
        logger.info(f"Subscription activated for {payment.user.username}")
        
    except Exception as e:
        logger.error(f"Error activating subscription: {e}")


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def payment_status(request, payment_id):
    """Get payment status"""
    try:
        payment = Payment.objects.get(
            payment_id=payment_id,
            user=request.user
        )
        
        return Response({
            'payment_id': payment.payment_id,
            'status': payment.status,
            'amount': payment.amount,
            'currency': payment.currency,
            'created_at': payment.created_at,
            'completed_at': payment.completed_at
        })
        
    except Payment.DoesNotExist:
        return Response(
            {'error': 'Payment not found'},
            status=status.HTTP_404_NOT_FOUND
        )
