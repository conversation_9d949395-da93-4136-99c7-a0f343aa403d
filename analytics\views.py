from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.http import HttpResponse
from django.utils import timezone
from datetime import timedelta
import csv

from .models import StreamerAnalytics
from streamers.models import Streamer


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def analytics_dashboard(request):
    """Get analytics data for streamer dashboard"""
    try:
        streamer = Streamer.objects.get(user=request.user)
    except Streamer.DoesNotExist:
        return Response({'error': 'Streamer profile not found'}, status=404)
    
    # Get date range (default to last 30 days)
    days = int(request.GET.get('days', 30))
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=days)
    
    # Get analytics data
    analytics = StreamerAnalytics.objects.filter(
        streamer=streamer,
        date__range=[start_date, end_date]
    ).order_by('date')
    
    # Aggregate data
    total_visitors = sum(a.unique_visitors for a in analytics)
    total_games = sum(a.total_games for a in analytics)
    total_points_distributed = sum(a.points_distributed for a in analytics)
    
    # Prepare chart data
    chart_data = {
        'dates': [a.date.strftime('%Y-%m-%d') for a in analytics],
        'visitors': [a.unique_visitors for a in analytics],
        'games': [a.total_games for a in analytics],
        'points': [a.points_distributed for a in analytics],
    }
    
    return Response({
        'summary': {
            'total_visitors': total_visitors,
            'total_games': total_games,
            'total_points_distributed': total_points_distributed,
            'avg_daily_visitors': total_visitors / days if days > 0 else 0,
        },
        'chart_data': chart_data,
        'period': {
            'start_date': start_date,
            'end_date': end_date,
            'days': days
        }
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def export_analytics(request):
    """Export analytics data as CSV"""
    try:
        streamer = Streamer.objects.get(user=request.user)
    except Streamer.DoesNotExist:
        return Response({'error': 'Streamer profile not found'}, status=404)
    
    # Get date range
    days = int(request.GET.get('days', 30))
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=days)
    
    # Get analytics data
    analytics = StreamerAnalytics.objects.filter(
        streamer=streamer,
        date__range=[start_date, end_date]
    ).order_by('date')
    
    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="analytics_{streamer.user.username}_{start_date}_{end_date}.csv"'
    
    writer = csv.writer(response)
    writer.writerow([
        'Date', 'Unique Visitors', 'New Users', 'Returning Users',
        'Total Games', 'Wheel Spins', 'Slot Games', 'Dice Games',
        'Points Distributed', 'Points Spent', 'Shop Purchases',
        'Chat Messages', 'Bot Responses', 'Commands Used'
    ])
    
    for a in analytics:
        writer.writerow([
            a.date, a.unique_visitors, a.new_users, a.returning_users,
            a.total_games, a.wheel_spins, a.slot_games, a.dice_games,
            a.points_distributed, a.points_spent, a.shop_purchases,
            a.chat_messages, a.bot_responses, a.commands_used
        ])
    
    return response
