# Generated by Django 4.2.7 on 2025-07-18 20:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("streamers", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="WebSocketConnection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("connection_id", models.UUIDField(default=uuid.uuid4, unique=True)),
                (
                    "connection_type",
                    models.CharField(
                        choices=[
                            ("streamer_dashboard", "Streamer Dashboard"),
                            ("viewer_page", "Viewer Page"),
                            ("obs_overlay", "OBS Overlay"),
                        ],
                        max_length=30,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("ip_address", models.GenericIPAddressField()),
                ("user_agent", models.TextField()),
                ("connected_at", models.DateTimeField(auto_now_add=True)),
                ("last_activity", models.DateTimeField(auto_now=True)),
                ("disconnected_at", models.DateTimeField(blank=True, null=True)),
                (
                    "streamer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="streamers.streamer",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="KickChatConnection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("kick_channel_id", models.CharField(max_length=100)),
                ("is_connected", models.BooleanField(default=False)),
                ("connection_token", models.CharField(blank=True, max_length=500)),
                (
                    "bot_username",
                    models.CharField(default="CasinoXBot", max_length=100),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("last_connected", models.DateTimeField(blank=True, null=True)),
                ("last_disconnected", models.DateTimeField(blank=True, null=True)),
                ("total_messages_processed", models.IntegerField(default=0)),
                ("last_error", models.TextField(blank=True)),
                ("error_count", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "streamer",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="streamers.streamer",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ChatMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("kick_message_id", models.CharField(max_length=100, unique=True)),
                ("username", models.CharField(max_length=100)),
                ("user_id", models.CharField(blank=True, max_length=100, null=True)),
                ("content", models.TextField()),
                (
                    "message_type",
                    models.CharField(
                        choices=[
                            ("message", "Regular Message"),
                            ("command", "Bot Command"),
                            ("verification", "Verification Code"),
                            ("system", "System Message"),
                        ],
                        default="message",
                        max_length=20,
                    ),
                ),
                ("is_processed", models.BooleanField(default=False)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                ("bot_response", models.TextField(blank=True)),
                ("timestamp", models.DateTimeField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "connection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="chat_bot.kickchatconnection",
                    ),
                ),
            ],
            options={
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="BotResponse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("response_text", models.TextField()),
                ("response_type", models.CharField(max_length=50)),
                ("was_sent", models.BooleanField(default=False)),
                ("send_error", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "connection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="chat_bot.kickchatconnection",
                    ),
                ),
                (
                    "original_message",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="chat_bot.chatmessage",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CommandCooldown",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("username", models.CharField(max_length=100)),
                ("command", models.CharField(max_length=50)),
                ("last_used", models.DateTimeField(auto_now_add=True)),
                (
                    "connection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="chat_bot.kickchatconnection",
                    ),
                ),
            ],
            options={
                "unique_together": {("connection", "username", "command")},
            },
        ),
        migrations.CreateModel(
            name="ChatAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField()),
                ("total_messages", models.IntegerField(default=0)),
                ("unique_users", models.IntegerField(default=0)),
                ("commands_used", models.IntegerField(default=0)),
                ("verifications_attempted", models.IntegerField(default=0)),
                ("verifications_successful", models.IntegerField(default=0)),
                ("bot_responses", models.IntegerField(default=0)),
                ("bot_errors", models.IntegerField(default=0)),
                ("command_usage", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "connection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="chat_bot.kickchatconnection",
                    ),
                ),
            ],
            options={
                "unique_together": {("connection", "date")},
            },
        ),
    ]
