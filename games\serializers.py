from rest_framework import serializers
from .models import Game<PERSON>ession, WheelSpin, SlotMachine, DiceRoll


class GameSessionSerializer(serializers.ModelSerializer):
    """Serializer for game sessions"""
    username = serializers.CharField(source='user.username', read_only=True)
    streamer_name = serializers.CharField(source='streamer.display_name', read_only=True)
    
    class Meta:
        model = GameSession
        fields = [
            'session_id', 'username', 'streamer_name', 'game_type',
            'bet_amount', 'potential_win', 'actual_win', 'status',
            'game_data', 'result_data', 'created_at', 'started_at',
            'completed_at'
        ]
        read_only_fields = ['session_id', 'created_at', 'started_at', 'completed_at']


class WheelSpinSerializer(serializers.ModelSerializer):
    """Serializer for wheel spins"""
    session = GameSessionSerializer(read_only=True)
    
    class Meta:
        model = WheelSpin
        fields = [
            'session', 'prizes', 'selected_prize_index', 'selected_prize',
            'spin_duration', 'final_rotation', 'created_at'
        ]


class CreateWheelSpinSerializer(serializers.Serializer):
    """Serializer for creating wheel spins"""
    bet_amount = serializers.IntegerField(min_value=1)
    streamer_username = serializers.CharField()


class SlotMachineSerializer(serializers.ModelSerializer):
    """Serializer for slot machine games"""
    session = GameSessionSerializer(read_only=True)
    
    class Meta:
        model = SlotMachine
        fields = [
            'session', 'reels', 'paylines', 'multiplier', 'symbols', 'created_at'
        ]


class CreateSlotGameSerializer(serializers.Serializer):
    """Serializer for creating slot games"""
    bet_amount = serializers.IntegerField(min_value=10, max_value=1000)
    streamer_username = serializers.CharField()


class DiceRollSerializer(serializers.ModelSerializer):
    """Serializer for dice roll games"""
    session = GameSessionSerializer(read_only=True)
    
    class Meta:
        model = DiceRoll
        fields = [
            'session', 'num_dice', 'dice_results', 'total_roll',
            'bet_type', 'bet_value', 'created_at'
        ]


class CreateDiceGameSerializer(serializers.Serializer):
    """Serializer for creating dice games"""
    bet_amount = serializers.IntegerField(min_value=5, max_value=500)
    bet_type = serializers.ChoiceField(choices=['over', 'under', 'exact'])
    bet_value = serializers.IntegerField(min_value=2, max_value=12)
    streamer_username = serializers.CharField()


class GameHistorySerializer(serializers.ModelSerializer):
    """Serializer for game history"""
    username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = GameSession
        fields = [
            'session_id', 'username', 'game_type', 'bet_amount',
            'actual_win', 'status', 'result_data', 'completed_at'
        ]
