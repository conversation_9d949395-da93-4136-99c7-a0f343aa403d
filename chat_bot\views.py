from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import logging
import re

from .models import KickChatConnection, ChatMessage, CommandCooldown, BotResponse, ChatAnalytics
from .serializers import (
    KickChatConnectionSerializer, ChatMessageSerializer, 
    BotResponseSerializer, ChatAnalyticsSerializer
)
from streamers.models import Streamer, ChatCommand
from accounts.models import VerificationCode

logger = logging.getLogger(__name__)


class ChatConnectionListView(generics.ListCreateAPIView):
    """List and create chat connections"""
    serializer_class = KickChatConnectionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        try:
            streamer = Streamer.objects.get(user=self.request.user)
            return KickChatConnection.objects.filter(streamer=streamer)
        except Streamer.DoesNotExist:
            return KickChatConnection.objects.none()

    def perform_create(self, serializer):
        streamer = get_object_or_404(Streamer, user=self.request.user)
        serializer.save(streamer=streamer)


class ChatConnectionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete chat connections"""
    serializer_class = KickChatConnectionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        try:
            streamer = Streamer.objects.get(user=self.request.user)
            return KickChatConnection.objects.filter(streamer=streamer)
        except Streamer.DoesNotExist:
            return KickChatConnection.objects.none()


class ChatMessageListView(generics.ListAPIView):
    """List chat messages for streamer"""
    serializer_class = ChatMessageSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        try:
            streamer = Streamer.objects.get(user=self.request.user)
            connection = KickChatConnection.objects.get(streamer=streamer)
            return ChatMessage.objects.filter(connection=connection).order_by('-timestamp')
        except (Streamer.DoesNotExist, KickChatConnection.DoesNotExist):
            return ChatMessage.objects.none()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_chat_message(request):
    """Send a message to Kick chat manually"""
    try:
        streamer = Streamer.objects.get(user=request.user)
        connection = KickChatConnection.objects.get(streamer=streamer)
    except (Streamer.DoesNotExist, KickChatConnection.DoesNotExist):
        return Response(
            {'error': 'Chat connection not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    message = request.data.get('message', '').strip()
    if not message:
        return Response(
            {'error': 'Message cannot be empty'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Send message to Kick chat (implement actual Kick API call)
        success = send_to_kick_chat(connection, message)
        
        if success:
            return Response({'message': 'Message sent successfully'})
        else:
            return Response(
                {'error': 'Failed to send message'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    except Exception as e:
        logger.error(f"Error sending chat message: {e}")
        return Response(
            {'error': 'Failed to send message'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def kick_chat_webhook(request):
    """Webhook endpoint for receiving Kick chat messages"""
    try:
        # Parse incoming message data
        message_data = request.data
        channel_id = message_data.get('channel_id')
        username = message_data.get('username')
        user_id = message_data.get('user_id')
        content = message_data.get('content', '').strip()
        message_id = message_data.get('message_id')
        timestamp = message_data.get('timestamp')

        # Find the chat connection
        try:
            connection = KickChatConnection.objects.get(
                kick_channel_id=channel_id,
                is_active=True
            )
        except KickChatConnection.DoesNotExist:
            logger.warning(f"No active connection found for channel {channel_id}")
            return Response({'status': 'ignored'})

        # Create chat message record
        chat_message = ChatMessage.objects.create(
            connection=connection,
            kick_message_id=message_id,
            username=username,
            user_id=user_id,
            content=content,
            timestamp=timezone.now() if not timestamp else timezone.datetime.fromisoformat(timestamp)
        )

        # Process the message
        process_chat_message(chat_message)

        return Response({'status': 'processed'})

    except Exception as e:
        logger.error(f"Error processing chat webhook: {e}")
        return Response(
            {'error': 'Webhook processing failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def process_chat_message(chat_message):
    """Process incoming chat message for commands and verification"""
    content = chat_message.content.lower().strip()
    connection = chat_message.connection
    streamer = connection.streamer

    # Check for verification code
    verify_match = re.match(r'!verify\s+([A-Z0-9]{6})', content.upper())
    if verify_match:
        verification_code = verify_match.group(1)
        handle_verification(chat_message, verification_code)
        return

    # Check for bot commands
    if content.startswith('!'):
        handle_bot_command(chat_message)
        return

    # Mark as processed
    chat_message.mark_processed()


def handle_verification(chat_message, verification_code):
    """Handle verification code from chat"""
    try:
        # Find the verification code
        verification = VerificationCode.objects.get(
            code=verification_code,
            streamer=chat_message.connection.streamer,
            is_used=False
        )

        if verification.is_expired():
            response = f"@{chat_message.username} Verification code has expired. Please generate a new one."
        else:
            # Use the verification code
            if verification.use_code(chat_message.username):
                response = f"@{chat_message.username} Successfully verified! Welcome to the casino! You received 100 bonus points! 🎉"
                
                # Send real-time update to viewer page
                channel_layer = get_channel_layer()
                async_to_sync(channel_layer.group_send)(
                    f'chat_{chat_message.connection.streamer.user.username}',
                    {
                        'type': 'verification_update',
                        'data': {
                            'user_id': verification.user.id,
                            'username': chat_message.username,
                            'verified': True,
                            'points': verification.user.points
                        }
                    }
                )
            else:
                response = f"@{chat_message.username} Failed to verify. Please try again."

    except VerificationCode.DoesNotExist:
        response = f"@{chat_message.username} Invalid verification code. Please check and try again."

    # Send response to chat
    send_to_kick_chat(chat_message.connection, response)
    chat_message.mark_processed(response)


def handle_bot_command(chat_message):
    """Handle bot commands from chat"""
    content = chat_message.content.lower().strip()
    connection = chat_message.connection
    streamer = connection.streamer
    username = chat_message.username

    # Extract command
    command_parts = content.split()
    command = command_parts[0]

    # Check cooldown
    if CommandCooldown.is_on_cooldown(connection, username, command, streamer.command_cooldown):
        return  # Ignore if on cooldown

    # Handle built-in commands
    response = None
    
    if command == '!points':
        response = handle_points_command(username, streamer)
    elif command == '!shop':
        response = handle_shop_command(streamer)
    elif command.startswith('!slot'):
        response = handle_slot_command(username, command_parts, streamer)
    elif command == '!leaderboard':
        response = handle_leaderboard_command(streamer)
    elif command == '!help':
        response = handle_help_command(streamer)

    # Check custom commands
    if not response:
        try:
            custom_command = ChatCommand.objects.get(
                streamer=streamer,
                command=command,
                is_enabled=True
            )
            response = custom_command.response
            
            # Update usage stats
            custom_command.usage_count += 1
            custom_command.last_used = timezone.now()
            custom_command.save()
            
        except ChatCommand.DoesNotExist:
            pass

    # Send response if we have one
    if response:
        send_to_kick_chat(connection, response)
        CommandCooldown.set_cooldown(connection, username, command)

    chat_message.mark_processed(response or "")


def handle_points_command(username, streamer):
    """Handle !points command"""
    try:
        from accounts.models import User
        user = User.objects.get(kick_username=username)
        return f"@{username} You have {user.points} points! 💰"
    except User.DoesNotExist:
        return f"@{username} You need to verify your Discord account first! Use the verification code from the website."


def handle_shop_command(streamer):
    """Handle !shop command"""
    if not streamer.shop_enabled:
        return "Shop is currently disabled."
    
    return f"Visit the shop at: https://casinox.com/s/{streamer.user.username} 🛒"


def handle_slot_command(username, command_parts, streamer):
    """Handle !slot command"""
    if not streamer.games_enabled:
        return "Games are currently disabled."
    
    if len(command_parts) < 2:
        return f"@{username} Usage: !slot <amount> (minimum 10 points)"
    
    try:
        bet_amount = int(command_parts[1])
        if bet_amount < 10:
            return f"@{username} Minimum bet is 10 points!"
        
        return f"@{username} Visit the website to play slots! Bet: {bet_amount} points 🎰"
    except ValueError:
        return f"@{username} Invalid bet amount. Use: !slot <amount>"


def handle_leaderboard_command(streamer):
    """Handle !leaderboard command"""
    return f"Check the leaderboard at: https://casinox.com/s/{streamer.user.username} 🏆"


def handle_help_command(streamer):
    """Handle !help command"""
    commands = ["!points", "!help"]
    
    if streamer.shop_enabled:
        commands.append("!shop")
    if streamer.games_enabled:
        commands.append("!slot <amount>")
    if streamer.leaderboard_enabled:
        commands.append("!leaderboard")
    
    return f"Available commands: {', '.join(commands)} | Visit: https://casinox.com/s/{streamer.user.username}"


def send_to_kick_chat(connection, message):
    """Send message to Kick chat (implement actual API call)"""
    try:
        # This would implement the actual Kick API call
        # For now, we'll just log it and return True
        logger.info(f"Sending to Kick chat ({connection.streamer.kick_channel}): {message}")
        
        # Create bot response record
        BotResponse.objects.create(
            connection=connection,
            response_text=message,
            response_type='auto_reply',
            was_sent=True
        )
        
        return True
    except Exception as e:
        logger.error(f"Error sending to Kick chat: {e}")
        return False


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def chat_analytics(request):
    """Get chat analytics for streamer"""
    try:
        streamer = Streamer.objects.get(user=request.user)
        connection = KickChatConnection.objects.get(streamer=streamer)
    except (Streamer.DoesNotExist, KickChatConnection.DoesNotExist):
        return Response(
            {'error': 'Chat connection not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Get date range (default to last 7 days)
    days = int(request.GET.get('days', 7))
    end_date = timezone.now().date()
    start_date = end_date - timezone.timedelta(days=days)

    # Get analytics data
    analytics = ChatAnalytics.objects.filter(
        connection=connection,
        date__range=[start_date, end_date]
    ).order_by('date')

    # Aggregate data
    total_messages = sum(a.total_messages for a in analytics)
    total_commands = sum(a.commands_used for a in analytics)
    total_verifications = sum(a.verifications_attempted for a in analytics)
    successful_verifications = sum(a.verifications_successful for a in analytics)

    return Response({
        'summary': {
            'total_messages': total_messages,
            'total_commands': total_commands,
            'total_verifications': total_verifications,
            'successful_verifications': successful_verifications,
            'verification_rate': (successful_verifications / total_verifications * 100) if total_verifications > 0 else 0
        },
        'daily_data': ChatAnalyticsSerializer(analytics, many=True).data,
        'period': {
            'start_date': start_date,
            'end_date': end_date,
            'days': days
        }
    })
