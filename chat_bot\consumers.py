import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from .models import WebSocketConnection
from streamers.models import Streamer, OBSOverlay

logger = logging.getLogger(__name__)
User = get_user_model()


class ChatConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for real-time chat updates on viewer pages"""
    
    async def connect(self):
        self.streamer_username = self.scope['url_route']['kwargs']['streamer_username']
        self.room_group_name = f'chat_{self.streamer_username}'
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Track connection
        await self.track_connection()

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        
        # Mark connection as disconnected
        await self.disconnect_tracking()

    async def receive(self, text_data):
        """Handle incoming WebSocket messages"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong'
                }))
            elif message_type == 'verification_status':
                # Update verification status for user
                await self.handle_verification_status(text_data_json)
                
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received: {text_data}")

    async def chat_message(self, event):
        """Send chat message to WebSocket"""
        await self.send(text_data=json.dumps(event))

    async def verification_update(self, event):
        """Send verification update to WebSocket"""
        await self.send(text_data=json.dumps(event))

    async def game_result(self, event):
        """Send game result to WebSocket"""
        await self.send(text_data=json.dumps(event))

    @database_sync_to_async
    def track_connection(self):
        """Track WebSocket connection in database"""
        try:
            streamer = Streamer.objects.get(user__username=self.streamer_username)
            user = self.scope.get('user') if self.scope['user'].is_authenticated else None
            
            WebSocketConnection.objects.create(
                user=user,
                streamer=streamer,
                connection_type='viewer_page',
                ip_address=self.scope.get('client', ['unknown'])[0],
                user_agent=dict(self.scope.get('headers', {})).get(b'user-agent', b'').decode()
            )
        except Exception as e:
            logger.error(f"Error tracking connection: {e}")

    @database_sync_to_async
    def disconnect_tracking(self):
        """Mark connection as disconnected"""
        # This would require storing connection_id in self during connect
        pass

    async def handle_verification_status(self, data):
        """Handle verification status updates"""
        # This would update user verification status
        pass


class DashboardConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for streamer dashboard real-time updates"""
    
    async def connect(self):
        self.streamer_username = self.scope['url_route']['kwargs']['streamer_username']
        self.room_group_name = f'dashboard_{self.streamer_username}'
        
        # Check if user is the streamer
        user = self.scope['user']
        if not user.is_authenticated or user.username != self.streamer_username:
            await self.close()
            return
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        """Handle incoming WebSocket messages from dashboard"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong'
                }))
            elif message_type == 'chat_command':
                # Handle manual chat commands from dashboard
                await self.handle_chat_command(text_data_json)
                
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received: {text_data}")

    async def analytics_update(self, event):
        """Send analytics update to dashboard"""
        await self.send(text_data=json.dumps(event))

    async def new_user(self, event):
        """Send new user notification to dashboard"""
        await self.send(text_data=json.dumps(event))

    async def game_activity(self, event):
        """Send game activity to dashboard"""
        await self.send(text_data=json.dumps(event))

    async def handle_chat_command(self, data):
        """Handle manual chat commands from dashboard"""
        # This would send commands to Kick chat
        pass


class OBSOverlayConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for OBS overlays"""
    
    async def connect(self):
        self.overlay_id = self.scope['url_route']['kwargs']['overlay_id']
        self.room_group_name = f'obs_{self.overlay_id}'
        
        # Verify overlay exists
        overlay_exists = await self.verify_overlay()
        if not overlay_exists:
            await self.close()
            return
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        """Handle incoming WebSocket messages from OBS"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong'
                }))
                
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received: {text_data}")

    async def overlay_update(self, event):
        """Send overlay update to OBS"""
        await self.send(text_data=json.dumps(event))

    async def wheel_spin(self, event):
        """Send wheel spin animation to OBS"""
        await self.send(text_data=json.dumps(event))

    async def game_result(self, event):
        """Send game result to OBS overlay"""
        await self.send(text_data=json.dumps(event))

    async def leaderboard_update(self, event):
        """Send leaderboard update to OBS"""
        await self.send(text_data=json.dumps(event))

    @database_sync_to_async
    def verify_overlay(self):
        """Verify that overlay exists and is active"""
        try:
            overlay = OBSOverlay.objects.get(
                overlay_id=self.overlay_id,
                is_enabled=True
            )
            return True
        except OBSOverlay.DoesNotExist:
            return False
