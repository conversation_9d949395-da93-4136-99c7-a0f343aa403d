from django.core.management.base import BaseCommand
from streamers.models import SubscriptionPlan


class Command(BaseCommand):
    help = 'Setup default subscription plans for CasinoX'

    def handle(self, *args, **options):
        plans = [
            {
                'name': 'basic',
                'display_name': 'Basic Plan',
                'price_monthly': 10.00,
                'features': {
                    'wheel': True,
                    'basic_chat': True,
                    'discord_integration': True,
                    'leaderboard': True,
                    'games': False,
                    'shop': False,
                    'obs_overlays': False,
                    'analytics': False,
                    'custom_commands': False,
                    'priority_support': False
                },
                'max_concurrent_users': 100
            },
            {
                'name': 'pro',
                'display_name': 'Pro Plan',
                'price_monthly': 25.00,
                'features': {
                    'wheel': True,
                    'basic_chat': True,
                    'discord_integration': True,
                    'leaderboard': True,
                    'games': True,
                    'shop': True,
                    'obs_overlays': False,
                    'analytics': True,
                    'custom_commands': True,
                    'priority_support': False
                },
                'max_concurrent_users': 500
            },
            {
                'name': 'ultimate',
                'display_name': 'Ultimate Plan',
                'price_monthly': 50.00,
                'features': {
                    'wheel': True,
                    'basic_chat': True,
                    'discord_integration': True,
                    'leaderboard': True,
                    'games': True,
                    'shop': True,
                    'obs_overlays': True,
                    'analytics': True,
                    'custom_commands': True,
                    'priority_support': True,
                    'api_access': True,
                    'white_label': True
                },
                'max_concurrent_users': 2000
            }
        ]

        for plan_data in plans:
            plan, created = SubscriptionPlan.objects.get_or_create(
                name=plan_data['name'],
                defaults=plan_data
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created plan: {plan.display_name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Plan already exists: {plan.display_name}')
                )

        self.stdout.write(
            self.style.SUCCESS('Successfully setup subscription plans')
        )
