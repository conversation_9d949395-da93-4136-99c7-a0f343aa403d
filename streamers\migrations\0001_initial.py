# Generated by Django 4.2.7 on 2025-07-18 20:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ShopConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_enabled", models.BooleanField(default=False)),
                ("welcome_message", models.TextField(default="Welcome to the shop!")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="ShopItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField()),
                ("price", models.IntegerField()),
                (
                    "image",
                    models.ImageField(blank=True, null=True, upload_to="shop/items/"),
                ),
                ("is_available", models.BooleanField(default=True)),
                ("stock_quantity", models.IntegerField(blank=True, null=True)),
                ("max_per_user", models.IntegerField(blank=True, null=True)),
                ("category", models.CharField(default="general", max_length=50)),
                ("sort_order", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "shop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="streamers.shopconfiguration",
                    ),
                ),
            ],
            options={
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="Streamer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("kick_channel", models.CharField(max_length=100, unique=True)),
                ("display_name", models.CharField(max_length=100)),
                ("bio", models.TextField(blank=True)),
                (
                    "avatar",
                    models.ImageField(
                        blank=True, null=True, upload_to="streamers/avatars/"
                    ),
                ),
                (
                    "banner",
                    models.ImageField(
                        blank=True, null=True, upload_to="streamers/banners/"
                    ),
                ),
                ("subscription_active", models.BooleanField(default=False)),
                (
                    "subscription_expires_at",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("timezone", models.CharField(default="UTC", max_length=50)),
                ("primary_color", models.CharField(default="#6366f1", max_length=7)),
                ("secondary_color", models.CharField(default="#8b5cf6", max_length=7)),
                ("wheel_enabled", models.BooleanField(default=True)),
                ("games_enabled", models.BooleanField(default=False)),
                ("shop_enabled", models.BooleanField(default=False)),
                ("leaderboard_enabled", models.BooleanField(default=True)),
                ("obs_overlays_enabled", models.BooleanField(default=False)),
                ("bot_enabled", models.BooleanField(default=True)),
                ("auto_reply_enabled", models.BooleanField(default=True)),
                ("command_cooldown", models.IntegerField(default=5)),
                ("total_viewers", models.IntegerField(default=0)),
                ("total_games_played", models.IntegerField(default=0)),
                ("total_points_distributed", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="SubscriptionPlan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        choices=[
                            ("basic", "Basic"),
                            ("pro", "Pro"),
                            ("ultimate", "Ultimate"),
                        ],
                        max_length=50,
                        unique=True,
                    ),
                ),
                ("display_name", models.CharField(max_length=100)),
                ("price_monthly", models.DecimalField(decimal_places=2, max_digits=10)),
                ("features", models.JSONField(default=dict)),
                ("max_concurrent_users", models.IntegerField(default=100)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="WheelConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("trigger_keyword", models.CharField(default="!wheel", max_length=50)),
                ("min_points_to_spin", models.IntegerField(default=10)),
                ("max_points_per_spin", models.IntegerField(default=100)),
                ("spin_cooldown", models.IntegerField(default=60)),
                ("auto_remove_idle", models.BooleanField(default=True)),
                ("idle_timeout", models.IntegerField(default=300)),
                ("prizes", models.JSONField(default=list)),
                ("wheel_colors", models.JSONField(default=list)),
                ("show_in_obs", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "streamer",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="streamers.streamer",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="VerificationCode",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("code", models.CharField(max_length=10, unique=True)),
                ("is_used", models.BooleanField(default=False)),
                ("expires_at", models.DateTimeField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("used_at", models.DateTimeField(blank=True, null=True)),
                (
                    "kick_username",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                (
                    "streamer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="streamers.streamer",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="UserSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_id", models.UUIDField(default=uuid.uuid4, unique=True)),
                ("ip_address", models.GenericIPAddressField()),
                ("user_agent", models.TextField()),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("ended_at", models.DateTimeField(blank=True, null=True)),
                (
                    "streamer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="streamers.streamer",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="streamer",
            name="subscription_plan",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="streamers.subscriptionplan",
            ),
        ),
        migrations.AddField(
            model_name="streamer",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.CreateModel(
            name="ShopPurchase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("quantity", models.IntegerField(default=1)),
                ("total_price", models.IntegerField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("purchase_id", models.UUIDField(default=uuid.uuid4, unique=True)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="streamers.shopitem",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="shopconfiguration",
            name="streamer",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to="streamers.streamer"
            ),
        ),
        migrations.CreateModel(
            name="OBSOverlay",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "overlay_type",
                    models.CharField(
                        choices=[
                            ("wheel", "Wheel of Fortune"),
                            ("leaderboard", "Leaderboard"),
                            ("recent_winner", "Recent Winner"),
                            ("shop_purchase", "Shop Purchase"),
                            ("game_result", "Game Result"),
                        ],
                        max_length=20,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("settings", models.JSONField(default=dict)),
                ("css_styles", models.TextField(blank=True)),
                ("is_enabled", models.BooleanField(default=True)),
                ("width", models.IntegerField(default=400)),
                ("height", models.IntegerField(default=300)),
                ("overlay_id", models.UUIDField(default=uuid.uuid4, unique=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "streamer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="obs_overlays",
                        to="streamers.streamer",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ChatCommand",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("command", models.CharField(max_length=50)),
                ("response", models.TextField()),
                ("is_enabled", models.BooleanField(default=True)),
                ("cooldown", models.IntegerField(default=5)),
                ("mod_only", models.BooleanField(default=False)),
                ("subscriber_only", models.BooleanField(default=False)),
                ("usage_count", models.IntegerField(default=0)),
                ("last_used", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "streamer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chat_commands",
                        to="streamers.streamer",
                    ),
                ),
            ],
            options={
                "unique_together": {("streamer", "command")},
            },
        ),
    ]
