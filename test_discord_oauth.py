#!/usr/bin/env python3
"""
Test Discord OAuth configuration
"""

import os
from decouple import config

def test_discord_config():
    """Test if Discord OAuth is properly configured"""
    print("🔐 Testing Discord OAuth Configuration")
    print("=" * 50)
    
    # Check environment variables
    client_id = config('DISCORD_CLIENT_ID', default='')
    client_secret = config('DISCORD_CLIENT_SECRET', default='')
    redirect_uri = config('DISCORD_REDIRECT_URI', default='')
    
    print(f"Client ID: {'✅ Set' if client_id and client_id != 'PASTE_YOUR_CLIENT_ID_HERE' else '❌ Not set'}")
    print(f"Client Secret: {'✅ Set' if client_secret and client_secret != 'PASTE_YOUR_CLIENT_SECRET_HERE' else '❌ Not set'}")
    print(f"Redirect URI: {'✅ Set' if redirect_uri else '❌ Not set'}")
    
    if client_id and client_id != 'PASTE_YOUR_CLIENT_ID_HERE':
        print(f"\nClient ID: {client_id}")
    else:
        print("\n❌ Please update your .env file with actual Discord credentials")
        print("1. Go to: https://discord.com/developers/applications")
        print("2. Create new application")
        print("3. Get Client ID and Secret")
        print("4. Update .env file")
        return False
    
    print(f"Redirect URI: {redirect_uri}")
    
    # Test Discord OAuth URL generation
    if client_id and client_id != 'PASTE_YOUR_CLIENT_ID_HERE':
        oauth_url = f"https://discord.com/api/oauth2/authorize?client_id={client_id}&redirect_uri={redirect_uri}&response_type=code&scope=identify"
        print(f"\n✅ Discord OAuth URL:")
        print(f"   {oauth_url}")
        print(f"\n🎯 Next Steps:")
        print(f"1. Visit: http://localhost:8000/s/teststreamer/")
        print(f"2. Click 'Login with Discord'")
        print(f"3. Test the authentication flow")
        return True
    
    return False

if __name__ == '__main__':
    test_discord_config()
