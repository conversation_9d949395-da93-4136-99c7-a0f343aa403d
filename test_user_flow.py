#!/usr/bin/env python3
"""
Test the complete user flow for CasinoX
"""

import os
import sys
import django
import requests
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'casinox.settings')
django.setup()

def test_api_endpoints():
    """Test API endpoints"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing API Endpoints")
    print("=" * 50)
    
    # Test public streamer API
    try:
        response = requests.get(f"{base_url}/s/teststreamer/api/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Streamer API: {data['display_name']}")
        else:
            print(f"❌ Streamer API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Streamer API error: {e}")
    
    # Test subscription plans API
    try:
        response = requests.get(f"{base_url}/api/streamers/plans/")
        if response.status_code == 200:
            plans = response.json()
            print(f"✅ Subscription Plans API: {len(plans)} plans available")
        else:
            print(f"❌ Plans API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Plans API error: {e}")


def test_database_data():
    """Test database has required data"""
    print("\n🗄️ Testing Database Data")
    print("=" * 50)
    
    from accounts.models import User
    from streamers.models import Streamer, SubscriptionPlan
    
    # Check users
    user_count = User.objects.count()
    streamer_count = Streamer.objects.count()
    plan_count = SubscriptionPlan.objects.count()
    
    print(f"✅ Users: {user_count}")
    print(f"✅ Streamers: {streamer_count}")
    print(f"✅ Subscription Plans: {plan_count}")
    
    # Check test streamer
    try:
        test_streamer = Streamer.objects.get(user__username='teststreamer')
        print(f"✅ Test Streamer: {test_streamer.display_name}")
        print(f"   - Kick Channel: {test_streamer.kick_channel}")
        print(f"   - Features Enabled: Wheel={test_streamer.wheel_enabled}, Games={test_streamer.games_enabled}")
    except Streamer.DoesNotExist:
        print("❌ Test streamer not found")


def create_sample_data():
    """Create sample data for testing"""
    print("\n🎲 Creating Sample Data")
    print("=" * 50)
    
    from streamers.models import Streamer, WheelConfiguration, ShopConfiguration, ShopItem
    
    try:
        test_streamer = Streamer.objects.get(user__username='teststreamer')
        
        # Create wheel configuration
        wheel_config, created = WheelConfiguration.objects.get_or_create(
            streamer=test_streamer,
            defaults={
                'trigger_keyword': '!spin',
                'min_points_to_spin': 10,
                'max_points_per_spin': 100,
                'spin_cooldown': 30,
                'prizes': [
                    {'name': '10 Points', 'value': 10, 'probability': 30},
                    {'name': '25 Points', 'value': 25, 'probability': 25},
                    {'name': '50 Points', 'value': 50, 'probability': 20},
                    {'name': '100 Points', 'value': 100, 'probability': 15},
                    {'name': '250 Points', 'value': 250, 'probability': 8},
                    {'name': 'JACKPOT!', 'value': 500, 'probability': 2},
                ],
                'wheel_colors': ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3']
            }
        )
        if created:
            print("✅ Created wheel configuration")
        
        # Create shop configuration
        shop_config, created = ShopConfiguration.objects.get_or_create(
            streamer=test_streamer,
            defaults={
                'is_enabled': True,
                'welcome_message': 'Welcome to the points shop! Spend your points on awesome rewards!'
            }
        )
        if created:
            print("✅ Created shop configuration")
        
        # Create sample shop items
        sample_items = [
            {'name': 'Discord VIP Role', 'description': 'Get VIP role in Discord server', 'price': 100, 'category': 'Discord'},
            {'name': 'Custom Emote Request', 'description': 'Request a custom emote for the channel', 'price': 250, 'category': 'Channel'},
            {'name': 'Game Choice', 'description': 'Choose the next game to play', 'price': 150, 'category': 'Stream'},
            {'name': 'Shoutout', 'description': 'Get a shoutout during stream', 'price': 75, 'category': 'Stream'},
            {'name': 'Song Request', 'description': 'Request a song to be played', 'price': 50, 'category': 'Music'},
        ]
        
        for item_data in sample_items:
            item, created = ShopItem.objects.get_or_create(
                shop=shop_config,
                name=item_data['name'],
                defaults=item_data
            )
            if created:
                print(f"✅ Created shop item: {item.name}")
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")


def print_next_steps():
    """Print next steps for the user"""
    print("\n🎯 Next Steps")
    print("=" * 50)
    
    print("1. 🔐 Configure Discord OAuth:")
    print("   - Go to: https://discord.com/developers/applications")
    print("   - Create app, get Client ID & Secret")
    print("   - Update .env file with real values")
    
    print("\n2. 🧪 Test User Authentication:")
    print("   - Visit: http://localhost:8000/s/teststreamer/")
    print("   - Click 'Login with Discord'")
    print("   - Generate verification code")
    print("   - Test in Kick chat: !verify CODE123")
    
    print("\n3. 🎮 Test Games:")
    print("   - Login as streamer: teststreamer/streamer123")
    print("   - Configure wheel prizes")
    print("   - Test games as viewer")
    
    print("\n4. 💰 Configure Payments:")
    print("   - Get NOWPayments API key for crypto")
    print("   - Get Stripe keys for credit cards")
    print("   - Update .env file")
    
    print("\n5. 🚀 Production Setup:")
    print("   - Set USE_SQLITE=False for PostgreSQL")
    print("   - Set USE_REDIS=True for real-time features")
    print("   - Configure domain and SSL")
    
    print("\n📱 URLs to Test:")
    print(f"   - Landing: http://localhost:8000/")
    print(f"   - Admin: http://localhost:8000/admin/")
    print(f"   - Test Streamer: http://localhost:8000/s/teststreamer/")
    print(f"   - Features: http://localhost:8000/about/")
    print(f"   - Pricing: http://localhost:8000/pricing/")


def main():
    """Run all tests and setup"""
    print("🎰 CasinoX User Flow Test")
    print("=" * 50)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_database_data()
    create_sample_data()
    test_api_endpoints()
    print_next_steps()
    
    print("\n" + "=" * 50)
    print("🎉 CasinoX is ready for testing!")
    print("Start with Discord OAuth setup for full functionality.")


if __name__ == '__main__':
    main()
